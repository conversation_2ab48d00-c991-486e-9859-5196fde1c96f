<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}
require_once(__DIR__ . "/../app/controllers/clientesController.php");
$clientes = getClientes($_SESSION["empresa"]);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Clientes - Nextor</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
<div class="wrapper">
    <?php include "layout/sidebar.php"; ?>
    <div class="main">
        <?php include "layout/header.php"; ?>
        <div class="container">
            <div class="page-header">
                <div class="page-title">
                    <h1>
                        <i class="fas fa-users"></i>
                        Clientes
                    </h1>
                    <p>Gerencie todos os clientes da empresa</p>
                </div>
                <div class="page-actions">
                    <a href="cliente_form.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Novo Cliente
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-list"></i>
                        Lista de Clientes
                    </h3>
                    <div class="card-actions">
                        <span class="badge">
                            <?= count($clientes) ?> clientes
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="produtos-grid">
                    <?php foreach ($clientes as $c): ?>
                        <div class="produto-card">
                            <h3><i class="fas fa-user"></i> <?= htmlspecialchars($c["nome"]) ?></h3>
                            <p><i class="fas fa-envelope"></i> <?= htmlspecialchars($c["email"] ?? 'Não informado') ?></p>
                            <p><i class="fas fa-phone"></i> <?= htmlspecialchars($c["telefone"] ?? 'Não informado') ?></p>
                            <span class="badge badge-success">Ativo</span>

                            <div class="card-actions">
                                <a href="cliente_form.php?id=<?= $c["id"] ?>" class="btn btn-outline"><i class="fas fa-edit"></i> Editar</a>
                                <a href="cliente_delete.php?id=<?= $c["id"] ?>" class="btn btn-danger" onclick="return confirm('Excluir cliente?')"><i class="fas fa-trash"></i> Excluir</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
