<?php
session_start();
require_once(__DIR__ . "/../app/controllers/relatoriosController.php");

$dataInicio = $_GET["inicio"] ?? date("Y-m-01");
$dataFim = $_GET["fim"] ?? date("Y-m-t");

$pedidos = relatorioPedidos($_SESSION["empresa"], $dataInicio, $dataFim);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Relatório de Pedidos</title>
</head>
<body>
    <h1>Relatório de Pedidos</h1>
    <form method="GET">
        De: <input type="date" name="inicio" value="<?= $dataInicio ?>">
        Até: <input type="date" name="fim" value="<?= $dataFim ?>">
        <button type="submit">Filtrar</button>
    </form>
    <hr>
    <table border="1" cellpadding="5" cellspacing="0">
        <tr><th>ID</th><th>Cliente</th><th>Data</th><th>Total</th></tr>
        <?php foreach ($pedidos as $p): ?>
        <tr>
            <td><?= $p["id"] ?></td>
            <td><?= $p["cliente_nome"] ?></td>
            <td><?= $p["data"] ?></td>
            <td>R$ <?= number_format($p["total"], 2, ',', '.') ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
</body>
</html>
