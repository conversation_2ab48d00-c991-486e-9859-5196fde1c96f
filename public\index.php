<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NEXTOR ERP - Sistema de Gestão Empresarial</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4a1a5c;
            --primary-light: #6b2c7a;
            --secondary: #ff6b35;
            --secondary-light: #ff8a5c;
            --accent: #ff4081;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #ffa726;
            --info: #42a5f5;
            --light: #f8fafc;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --white: #ffffff;
            --gradient-primary: linear-gradient(135deg, #ff6b35 0%, #ff4081 50%, #4a1a5c 100%);
            --gradient-secondary: linear-gradient(135deg, #ffa726 0%, #ff6b35 100%);
            --gradient-hero: linear-gradient(135deg, #ffb347 0%, #ff6b35 25%, #ff4081 75%, #4a1a5c 100%);
            --gradient-nextor: linear-gradient(135deg, #ffb347 0%, #ff6b35 25%, #ff4081 75%, #4a1a5c 100%);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--gray-900);
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--gray-200);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .header.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary);
            text-decoration: none;
        }

        .logo img {
            height: 40px;
            margin-right: 12px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--gray-700);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--secondary);
        }

        .btn-login {
            background: var(--gradient-primary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Hero Section */
        .hero {
            background: var(--gradient-hero);
            padding: 8rem 2rem 6rem;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: white;
            color: var(--secondary);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <nav class="nav">
            <a href="#" class="logo">
                <img src="assets/img/logo.png" alt="NEXTOR ERP Logo">
                NEXTOR
            </a>
            <ul class="nav-links">
                <li><a href="#recursos">Recursos</a></li>
                <li><a href="#solucoes">Soluções</a></li>
                <li><a href="#precos">Preços</a></li>
                <li><a href="#contato">Contato</a></li>
                <li><a href="login.php" class="btn-login">Entrar</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>Gestão Empresarial Inteligente</h1>
            <p>Transforme sua empresa com o NEXTOR ERP - o sistema completo de gestão que simplifica processos, aumenta a produtividade e impulsiona seus resultados.</p>
            <div class="hero-buttons">
                <a href="login.php" class="btn btn-primary">
                    <i class="fas fa-rocket"></i>
                    Começar Agora
                </a>
                <a href="#recursos" class="btn btn-secondary">
                    <i class="fas fa-play"></i>
                    Ver Demonstração
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="recursos" style="padding: 6rem 2rem; background: var(--gray-50);">
        <div style="max-width: 1200px; margin: 0 auto;">
            <div style="text-align: center; margin-bottom: 4rem;">
                <h2 style="font-size: 2.5rem; font-weight: 700; color: var(--primary); margin-bottom: 1rem;">
                    Recursos Poderosos
                </h2>
                <p style="font-size: 1.125rem; color: var(--gray-600); max-width: 600px; margin: 0 auto;">
                    Tudo que sua empresa precisa em uma única plataforma integrada
                </p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div style="background: white; padding: 2rem; border-radius: 16px; box-shadow: var(--shadow); text-align: center; transition: transform 0.3s ease;">
                    <div style="width: 80px; height: 80px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 2rem;">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--primary); margin-bottom: 1rem;">
                        Gestão de Clientes
                    </h3>
                    <p style="color: var(--gray-600); line-height: 1.6;">
                        Cadastre, organize e acompanhe todos os seus clientes com histórico completo de interações e vendas.
                    </p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 16px; box-shadow: var(--shadow); text-align: center; transition: transform 0.3s ease;">
                    <div style="width: 80px; height: 80px; background: var(--gradient-secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 2rem;">
                        <i class="fas fa-box"></i>
                    </div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--primary); margin-bottom: 1rem;">
                        Controle de Estoque
                    </h3>
                    <p style="color: var(--gray-600); line-height: 1.6;">
                        Gerencie produtos, controle estoque em tempo real e receba alertas automáticos de reposição.
                    </p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 16px; box-shadow: var(--shadow); text-align: center; transition: transform 0.3s ease;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 2rem;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--primary); margin-bottom: 1rem;">
                        Relatórios Inteligentes
                    </h3>
                    <p style="color: var(--gray-600); line-height: 1.6;">
                        Dashboards e relatórios em tempo real para tomada de decisões estratégicas baseadas em dados.
                    </p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 16px; box-shadow: var(--shadow); text-align: center; transition: transform 0.3s ease;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 2rem;">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--primary); margin-bottom: 1rem;">
                        Gestão Financeira
                    </h3>
                    <p style="color: var(--gray-600); line-height: 1.6;">
                        Controle completo de receitas, despesas, fluxo de caixa e conciliação bancária automatizada.
                    </p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 16px; box-shadow: var(--shadow); text-align: center; transition: transform 0.3s ease;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 2rem;">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--primary); margin-bottom: 1rem;">
                        Vendas & Pedidos
                    </h3>
                    <p style="color: var(--gray-600); line-height: 1.6;">
                        Processo completo de vendas, desde orçamentos até faturamento, com controle total de pedidos.
                    </p>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 16px; box-shadow: var(--shadow); text-align: center; transition: transform 0.3s ease;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 2rem;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--primary); margin-bottom: 1rem;">
                        Acesso Mobile
                    </h3>
                    <p style="color: var(--gray-600); line-height: 1.6;">
                        Interface responsiva que funciona perfeitamente em qualquer dispositivo, a qualquer hora.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section style="padding: 6rem 2rem; background: var(--primary); color: white; text-align: center;">
        <div style="max-width: 800px; margin: 0 auto;">
            <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">
                Pronto para Transformar sua Empresa?
            </h2>
            <p style="font-size: 1.125rem; margin-bottom: 2rem; opacity: 0.9;">
                Junte-se a milhares de empresas que já escolheram o NEXTOR ERP para otimizar sua gestão
            </p>
            <a href="login.php" style="background: white; color: var(--primary); padding: 1rem 2rem; border-radius: 12px; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease; box-shadow: var(--shadow-lg);">
                <i class="fas fa-rocket"></i>
                Começar Gratuitamente
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--gray-900); color: white; padding: 3rem 2rem 1rem; text-align: center;">
        <div style="max-width: 1200px; margin: 0 auto;">
            <div style="display: flex; justify-content: center; align-items: center; margin-bottom: 2rem;">
                <img src="assets/img/logo.png" alt="NEXTOR ERP Logo" style="height: 40px; margin-right: 12px;">
                <span style="font-size: 1.5rem; font-weight: 700;">NEXTOR</span>
            </div>
            <p style="color: var(--gray-400); margin-bottom: 2rem;">
                Sistema de Gestão Empresarial Inteligente
            </p>
            <div style="border-top: 1px solid var(--gray-700); padding-top: 2rem; color: var(--gray-500);">
                <p>&copy; 2025 NEXTOR ERP. Todos os direitos reservados.</p>
            </div>
        </div>
    </footer>

    <script>
        // Header scroll effect
        window.addEventListener('scroll', function() {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Card hover effects
        document.querySelectorAll('[style*="transition: transform"]').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px)';
            });
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
