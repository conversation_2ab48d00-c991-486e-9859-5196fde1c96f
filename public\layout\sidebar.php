<div class="sidebar">
    <div style="display: flex; align-items: center; justify-content: center; padding: 32px 24px 24px; border-bottom: 1px solid rgba(255, 107, 53, 0.3); margin-bottom: 16px;">
        <img src="assets/img/logo.png" alt="NEXTOR ERP Logo" style="height: 40px; margin-right: 12px; filter: brightness(1.2);">
        <h2 style="margin: 0; font-size: 18px; font-weight: 700; color: var(--white); text-shadow: 0 2px 4px rgba(0,0,0,0.3);">NEXTOR</h2>
    </div>
    <a href="dashboard.php" class="<?= basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : '' ?>">
        <i class="fas fa-chart-pie"></i>
        <span>Dashboard</span>
    </a>
    <a href="clientes.php" class="<?= basename($_SERVER['PHP_SELF']) == 'clientes.php' ? 'active' : '' ?>">
        <i class="fas fa-users"></i>
        <span>Clientes</span>
    </a>
    <a href="produtos.php" class="<?= basename($_SERVER['PHP_SELF']) == 'produtos.php' ? 'active' : '' ?>">
        <i class="fas fa-box"></i>
        <span>Produtos</span>
    </a>
    <a href="estoque.php" class="<?= basename($_SERVER['PHP_SELF']) == 'estoque.php' ? 'active' : '' ?>">
        <i class="fas fa-warehouse"></i>
        <span>Estoque</span>
    </a>
    <a href="pedidos.php" class="<?= basename($_SERVER['PHP_SELF']) == 'pedidos.php' ? 'active' : '' ?>">
        <i class="fas fa-shopping-cart"></i>
        <span>Pedidos</span>
    </a>
    <a href="financeiro.php" class="<?= basename($_SERVER['PHP_SELF']) == 'financeiro.php' ? 'active' : '' ?>">
        <i class="fas fa-wallet"></i>
        <span>Financeiro</span>
    </a>
    <a href="relatorios.php" class="<?= basename($_SERVER['PHP_SELF']) == 'relatorios.php' ? 'active' : '' ?>">
        <i class="fas fa-chart-line"></i>
        <span>Relatórios</span>
    </a>
</div>
