<?php
require_once(__DIR__ . "/../config/conexao.php");

// =====================================================
// GESTÃO DE CATEGORIAS
// =====================================================

function getCategorias($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->query("SELECT * FROM categorias WHERE ativo = true ORDER BY nome ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addCategoria($empresaBanco, $nome, $descricao, $cor, $icone) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("INSERT INTO categorias (nome, descricao, cor, icone) VALUES (?, ?, ?, ?)");
    return $stmt->execute([$nome, $descricao, $cor, $icone]);
}

function updateCategoria($empresaBanco, $id, $nome, $descricao, $cor, $icone) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("UPDATE categorias SET nome = ?, descricao = ?, cor = ?, icone = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
    return $stmt->execute([$nome, $descricao, $cor, $icone, $id]);
}

function deleteCategoria($empresaBanco, $id) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("UPDATE categorias SET ativo = false WHERE id = ?");
    return $stmt->execute([$id]);
}

// =====================================================
// GESTÃO DE MOVIMENTAÇÕES
// =====================================================

function getMovimentacoes($empresaBanco, $limite = 50) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("
        SELECT m.*, p.descricao as produto_nome, c.nome as categoria_nome, c.cor as categoria_cor
        FROM movimentacoes_estoque m
        JOIN produtos p ON m.produto_id = p.id
        LEFT JOIN categorias c ON p.categoria_id = c.id
        ORDER BY m.data_movimentacao DESC
        LIMIT ?
    ");
    $stmt->execute([$limite]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addMovimentacao($empresaBanco, $produto_id, $tipo, $quantidade, $motivo, $observacoes = '', $usuario = 'Sistema', $documento = '') {
    $conn = getEmpresaConnection($empresaBanco);
    
    // Buscar estoque atual
    $stmt = $conn->prepare("SELECT estoque FROM produtos WHERE id = ?");
    $stmt->execute([$produto_id]);
    $produto = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$produto) {
        throw new Exception("Produto não encontrado");
    }
    
    $quantidade_anterior = $produto['estoque'];
    
    // Calcular nova quantidade baseada no tipo
    if ($tipo == 'entrada') {
        $quantidade_atual = $quantidade_anterior + $quantidade;
    } elseif ($tipo == 'saida') {
        $quantidade_atual = $quantidade_anterior - $quantidade;
        if ($quantidade_atual < 0) {
            throw new Exception("Estoque insuficiente");
        }
    } else { // ajuste
        $quantidade_atual = $quantidade;
        $quantidade = $quantidade_atual - $quantidade_anterior; // Diferença
    }
    
    // Iniciar transação
    $conn->beginTransaction();
    
    try {
        // Inserir movimentação
        $stmt = $conn->prepare("
            INSERT INTO movimentacoes_estoque 
            (produto_id, tipo, quantidade, quantidade_anterior, quantidade_atual, motivo, observacoes, usuario, documento)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$produto_id, $tipo, $quantidade, $quantidade_anterior, $quantidade_atual, $motivo, $observacoes, $usuario, $documento]);
        
        // Atualizar estoque do produto
        $stmt = $conn->prepare("UPDATE produtos SET estoque = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$quantidade_atual, $produto_id]);
        
        $conn->commit();
        return true;
        
    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
}

// =====================================================
// RELATÓRIOS DE ESTOQUE
// =====================================================

function getEstoqueBaixo($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->query("
        SELECT p.*, c.nome as categoria_nome, c.cor as categoria_cor
        FROM produtos p
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE p.ativo = true AND p.estoque <= p.estoque_minimo
        ORDER BY p.estoque ASC
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getEstoqueAlto($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->query("
        SELECT p.*, c.nome as categoria_nome, c.cor as categoria_cor
        FROM produtos p
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE p.ativo = true AND p.estoque >= p.estoque_maximo
        ORDER BY p.estoque DESC
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getResumoEstoque($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    
    // Total de produtos
    $stmt = $conn->query("SELECT COUNT(*) as total FROM produtos WHERE ativo = true");
    $total_produtos = $stmt->fetch()['total'];
    
    // Produtos com estoque baixo
    $stmt = $conn->query("SELECT COUNT(*) as total FROM produtos WHERE ativo = true AND estoque <= estoque_minimo");
    $estoque_baixo = $stmt->fetch()['total'];
    
    // Produtos com estoque alto
    $stmt = $conn->query("SELECT COUNT(*) as total FROM produtos WHERE ativo = true AND estoque >= estoque_maximo");
    $estoque_alto = $stmt->fetch()['total'];
    
    // Valor total do estoque
    $stmt = $conn->query("SELECT SUM(estoque * preco) as valor_total FROM produtos WHERE ativo = true");
    $valor_total = $stmt->fetch()['valor_total'] ?? 0;
    
    // Movimentações do mês
    $stmt = $conn->query("
        SELECT COUNT(*) as total 
        FROM movimentacoes_estoque 
        WHERE data_movimentacao >= DATE_TRUNC('month', CURRENT_DATE)
    ");
    $movimentacoes_mes = $stmt->fetch()['total'];
    
    return [
        'total_produtos' => $total_produtos,
        'estoque_baixo' => $estoque_baixo,
        'estoque_alto' => $estoque_alto,
        'valor_total' => $valor_total,
        'movimentacoes_mes' => $movimentacoes_mes
    ];
}

function getProdutosPorCategoria($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->query("
        SELECT c.nome, c.cor, COUNT(p.id) as total_produtos, SUM(p.estoque) as total_estoque
        FROM categorias c
        LEFT JOIN produtos p ON c.id = p.categoria_id AND p.ativo = true
        WHERE c.ativo = true
        GROUP BY c.id, c.nome, c.cor
        ORDER BY total_produtos DESC
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
