<?php
/**
 * Conexão dinâmica com banco de dados da empresa
 * Cada empresa tem o seu próprio banco (ex: erp_empresa_1, erp_empresa_2...)
 */

function getEmpresaConnection(string $banco_nome)
{
    $host = "localhost";    // servidor
    $port = "5432";         // porta padrão do PostgreSQL
    $user = "postgres";     // usuário do PostgreSQL
    $password = "123!asd"; // senha do PostgreSQL (troque pela sua)

    try {
        $dsn = "pgsql:host=$host;port=$port;dbname=$banco_nome";
        $conn = new PDO($dsn, $user, $password);

        // Configurar PDO para lançar exceções em caso de erro
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        return $conn;
    } catch (PDOException $e) {
        die("❌ Erro ao conectar no banco da empresa [$banco_nome]: " . $e->getMessage());
    }
}
