-- Substitua {NOME_BANCO} por algo como erp_empresa_1
CREATE DATABASE {NOME_BANCO}
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'pt_BR.UTF-8'
    LC_CTYPE = 'pt_BR.UTF-8'
    TEMPLATE template0;

\c {NOME_BANCO};

-- <PERSON><PERSON><PERSON><PERSON><PERSON> da empresa
CREATE TABLE usuarios (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    senha VARCHAR(255) NOT NULL,
    nivel_acesso VARCHAR(20) DEFAULT 'usuario', -- admin, gerente, usuario
    ativo BOOLEAN DEFAULT TRUE
);

-- Clientes
CREATE TABLE clientes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(150) NOT NULL,
    telefone VARCHAR(20),
    email VARCHAR(100),
    endereco TEXT
);

-- Produtos
CREATE TABLE produtos (
    id SERIAL PRIMARY KEY,
    descricao VARCHAR(200) NOT NULL,
    preco NUMERIC(10,2) DEFAULT 0,
    estoque INT DEFAULT 0
);

-- Pedidos
CREATE TABLE pedidos (
    id SERIAL PRIMARY KEY,
    cliente_id INT REFERENCES clientes(id),
    data TIMESTAMP DEFAULT NOW(),
    total NUMERIC(10,2) DEFAULT 0
);

-- Itens de pedidos
CREATE TABLE pedidos_itens (
    id SERIAL PRIMARY KEY,
    pedido_id INT NOT NULL REFERENCES pedidos(id),
    produto_id INT NOT NULL REFERENCES produtos(id),
    quantidade INT NOT NULL,
    preco NUMERIC(10,2) NOT NULL
);

-- Financeiro
CREATE TABLE financeiro (
    id SERIAL PRIMARY KEY,
    tipo VARCHAR(10) NOT NULL, -- pagar, receber
    descricao VARCHAR(200),
    valor NUMERIC(10,2) NOT NULL,
    vencimento DATE,
    status VARCHAR(20) DEFAULT 'pendente'
);
