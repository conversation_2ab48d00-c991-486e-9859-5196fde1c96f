<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once("../app/controllers/clientesController.php");

$edit = false;
$nome = $telefone = $email = $endereco = "";
$id = null;

if (isset($_GET["id"])) {
    $edit = true;
    $conn = getEmpresaConnection($_SESSION["empresa"]);
    $stmt = $conn->prepare("SELECT * FROM clientes WHERE id = :id");
    $stmt->bindParam(":id", $_GET["id"]);
    $stmt->execute();
    $cliente = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($cliente) {
        $id = $cliente["id"];
        $nome = $cliente["nome"];
        $telefone = $cliente["telefone"];
        $email = $cliente["email"];
        $endereco = $cliente["endereco"];
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $nome = $_POST["nome"];
    $telefone = $_POST["telefone"];
    $email = $_POST["email"];
    $endereco = $_POST["endereco"];

    if ($edit) {
        updateCliente($_SESSION["empresa"], $id, $nome, $telefone, $email, $endereco);
    } else {
        addCliente($_SESSION["empresa"], $nome, $telefone, $email, $endereco);
    }
    header("Location: clientes.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $edit ? "Editar Cliente" : "Novo Cliente" ?> - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>

            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-<?= $edit ? 'edit' : 'user-plus' ?>"></i>
                            <?= $edit ? "Editar Cliente" : "Novo Cliente" ?>
                        </h1>
                        <p>Preencha os dados do cliente abaixo</p>
                    </div>
                    <div class="page-actions">
                        <a href="clientes.php" class="btn btn-outline">
                            <i class="fas fa-arrow-left"></i>
                            Voltar
                        </a>
                    </div>
                </div>

                <div class="form-container">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user"></i>
                                Dados do Cliente
                            </h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="form-modern">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="nome" class="form-label">
                                            <i class="fas fa-user"></i>
                                            Nome Completo *
                                        </label>
                                        <input
                                            type="text"
                                            id="nome"
                                            name="nome"
                                            class="form-control"
                                            value="<?= htmlspecialchars($nome) ?>"
                                            placeholder="Digite o nome completo"
                                            required
                                        >
                                    </div>

                                    <div class="form-group">
                                        <label for="telefone" class="form-label">
                                            <i class="fas fa-phone"></i>
                                            Telefone
                                        </label>
                                        <input
                                            type="tel"
                                            id="telefone"
                                            name="telefone"
                                            class="form-control"
                                            value="<?= htmlspecialchars($telefone) ?>"
                                            placeholder="(11) 99999-9999"
                                        >
                                    </div>

                                    <div class="form-group">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope"></i>
                                            E-mail
                                        </label>
                                        <input
                                            type="email"
                                            id="email"
                                            name="email"
                                            class="form-control"
                                            value="<?= htmlspecialchars($email) ?>"
                                            placeholder="<EMAIL>"
                                        >
                                    </div>

                                    <div class="form-group form-group-full">
                                        <label for="endereco" class="form-label">
                                            <i class="fas fa-map-marker-alt"></i>
                                            Endereço
                                        </label>
                                        <textarea
                                            id="endereco"
                                            name="endereco"
                                            class="form-control"
                                            rows="3"
                                            placeholder="Rua, número, bairro, cidade - CEP"
                                        ><?= htmlspecialchars($endereco) ?></textarea>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        <?= $edit ? "Atualizar Cliente" : "Cadastrar Cliente" ?>
                                    </button>
                                    <a href="clientes.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        Cancelar
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Máscara para telefone
        document.getElementById('telefone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 11) {
                value = value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
                if (value.length < 14) {
                    value = value.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
                }
                e.target.value = value;
            }
        });

        // Animação de foco nos inputs
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            // Se já tem valor, manter focado
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });
    </script>
</body>
</html>
