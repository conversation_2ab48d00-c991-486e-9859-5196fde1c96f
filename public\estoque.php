<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/estoqueController.php");

// Buscar dados
$resumo = getResumoEstoque($_SESSION["empresa"]);
$estoque_baixo = getEstoqueBaixo($_SESSION["empresa"]);
$estoque_alto = getEstoqueAlto($_SESSION["empresa"]);
$movimentacoes = getMovimentacoes($_SESSION["empresa"], 10);
$categorias = getCategorias($_SESSION["empresa"]);
$produtos_categoria = getProdutosPorCategoria($_SESSION["empresa"]);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Estoque - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>
            
            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-warehouse"></i>
                            Gestão de Estoque
                        </h1>
                        <p>Controle avançado de estoque e movimentações</p>
                    </div>
                    <div class="page-actions">
                        <a href="estoque_movimentacao.php" class="btn btn-secondary">
                            <i class="fas fa-exchange-alt"></i>
                            Nova Movimentação
                        </a>
                        <a href="categorias.php" class="btn btn-outline">
                            <i class="fas fa-tags"></i>
                            Categorias
                        </a>
                    </div>
                </div>

                <!-- Resumo do Estoque -->
                <div class="estoque-resumo">
                    <div class="resumo-card">
                        <div class="resumo-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="resumo-info">
                            <h3>Total de Produtos</h3>
                            <div class="resumo-valor"><?= number_format($resumo['total_produtos']) ?></div>
                            <div class="resumo-status">Produtos ativos</div>
                        </div>
                    </div>

                    <div class="resumo-card">
                        <div class="resumo-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="resumo-info">
                            <h3>Estoque Baixo</h3>
                            <div class="resumo-valor"><?= number_format($resumo['estoque_baixo']) ?></div>
                            <div class="resumo-status">Produtos em falta</div>
                        </div>
                    </div>

                    <div class="resumo-card">
                        <div class="resumo-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="resumo-info">
                            <h3>Valor do Estoque</h3>
                            <div class="resumo-valor">R$ <?= number_format($resumo['valor_total'], 2, ',', '.') ?></div>
                            <div class="resumo-status">Valor total investido</div>
                        </div>
                    </div>

                    <div class="resumo-card">
                        <div class="resumo-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="resumo-info">
                            <h3>Movimentações</h3>
                            <div class="resumo-valor"><?= number_format($resumo['movimentacoes_mes']) ?></div>
                            <div class="resumo-status">Este mês</div>
                        </div>
                    </div>
                </div>

                <!-- Grid Principal -->
                <div class="estoque-grid">
                    <!-- Produtos com Estoque Baixo -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-exclamation-triangle" style="color: #ef4444;"></i>
                                Estoque Baixo
                            </h3>
                            <div class="card-actions">
                                <span class="badge badge-danger"><?= count($estoque_baixo) ?> produtos</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($estoque_baixo)): ?>
                                <div class="empty-state-mini">
                                    <i class="fas fa-check-circle"></i>
                                    <p>Todos os produtos com estoque adequado!</p>
                                </div>
                            <?php else: ?>
                                <div class="produtos-lista">
                                    <?php foreach ($estoque_baixo as $produto): ?>
                                        <div class="produto-item">
                                            <div class="produto-info">
                                                <h4><?= htmlspecialchars($produto['descricao']) ?></h4>
                                                <span class="categoria" style="color: <?= $produto['categoria_cor'] ?? '#6b7280' ?>">
                                                    <?= htmlspecialchars($produto['categoria_nome'] ?? 'Sem categoria') ?>
                                                </span>
                                            </div>
                                            <div class="produto-estoque">
                                                <span class="estoque-atual"><?= $produto['estoque'] ?></span>
                                                <span class="estoque-minimo">/ <?= $produto['estoque_minimo'] ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Últimas Movimentações -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-history"></i>
                                Últimas Movimentações
                            </h3>
                            <div class="card-actions">
                                <a href="estoque_movimentacoes.php" class="btn btn-sm">Ver todas</a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($movimentacoes)): ?>
                                <div class="empty-state-mini">
                                    <i class="fas fa-clipboard-list"></i>
                                    <p>Nenhuma movimentação registrada</p>
                                </div>
                            <?php else: ?>
                                <div class="movimentacoes-lista">
                                    <?php foreach ($movimentacoes as $mov): ?>
                                        <div class="movimentacao-item">
                                            <div class="mov-icon <?= $mov['tipo'] ?>">
                                                <i class="fas fa-<?= $mov['tipo'] == 'entrada' ? 'arrow-up' : ($mov['tipo'] == 'saida' ? 'arrow-down' : 'edit') ?>"></i>
                                            </div>
                                            <div class="mov-info">
                                                <h4><?= htmlspecialchars($mov['produto_nome']) ?></h4>
                                                <span class="mov-detalhes">
                                                    <?= ucfirst($mov['tipo']) ?> de <?= abs($mov['quantidade']) ?> unidades
                                                    • <?= htmlspecialchars($mov['motivo']) ?>
                                                </span>
                                                <span class="mov-data"><?= date('d/m/Y H:i', strtotime($mov['data_movimentacao'])) ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Produtos por Categoria -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie"></i>
                            Produtos por Categoria
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="categorias-grid">
                            <?php foreach ($produtos_categoria as $cat): ?>
                                <div class="categoria-card">
                                    <div class="categoria-header" style="background: <?= $cat['cor'] ?>">
                                        <h4><?= htmlspecialchars($cat['nome']) ?></h4>
                                    </div>
                                    <div class="categoria-stats">
                                        <div class="stat">
                                            <span class="stat-label">Produtos</span>
                                            <span class="stat-value"><?= $cat['total_produtos'] ?></span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-label">Estoque</span>
                                            <span class="stat-value"><?= $cat['total_estoque'] ?></span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
