<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

// Aqui você pode puxar estatísticas do banco:
$stats = [
    "clientes" => 120,
    "produtos" => 350,
    "pedidos" => 45,
    "receita" => 15230.75
];
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Dashboard - Nextor</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
<div class="wrapper">
    <?php include "layout/sidebar.php"; ?>
    <div class="main">
        <?php include "layout/header.php"; ?>
        <div class="container">
            <div class="card fade-in-up">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-chart-pie"></i>
                        Visão Geral do Sistema
                    </h2>
                </div>
                <p style="color: var(--gray-600); margin: 0;">Acompanhe os principais indicadores do seu negócio em tempo real.</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <h2 class="stat-value"><?= $stats["clientes"] ?></h2>
                    <p class="stat-label">Total de Clientes</p>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +12% este mês
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                    </div>
                    <h2 class="stat-value"><?= $stats["produtos"] ?></h2>
                    <p class="stat-label">Produtos Cadastrados</p>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +8% este mês
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                    <h2 class="stat-value"><?= $stats["pedidos"] ?></h2>
                    <p class="stat-label">Pedidos Ativos</p>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +25% este mês
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <h2 class="stat-value">R$ <?= number_format($stats["receita"],0,",",".") ?></h2>
                    <p class="stat-label">Receita Total</p>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +18% este mês
                    </div>
                </div>
            </div>

        </div>
        <?php include "layout/footer.php"; ?>
    </div>
</div>
</body>
</html>
