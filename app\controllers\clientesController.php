<?php
require_once(__DIR__ . "/../config/conexao.php");
require_once(__DIR__ . "/../models/Cliente.php");


function getClientes($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->query("SELECT * FROM clientes ORDER BY id DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addCliente($empresaBanco, $nome, $telefone, $email, $endereco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("INSERT INTO clientes (nome, telefone, email, endereco) VALUES (:nome, :telefone, :email, :endereco)");
    $stmt->bindParam(":nome", $nome);
    $stmt->bindParam(":telefone", $telefone);
    $stmt->bindParam(":email", $email);
    $stmt->bindParam(":endereco", $endereco);
    $stmt->execute();
}

function updateCliente($empresaBanco, $id, $nome, $telefone, $email, $endereco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("UPDATE clientes SET nome = :nome, telefone = :telefone, email = :email, endereco = :endereco WHERE id = :id");
    $stmt->bindParam(":nome", $nome);
    $stmt->bindParam(":telefone", $telefone);
    $stmt->bindParam(":email", $email);
    $stmt->bindParam(":endereco", $endereco);
    $stmt->bindParam(":id", $id);
    $stmt->execute();
}

function deleteCliente($empresaBanco, $id) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("DELETE FROM clientes WHERE id = :id");
    $stmt->bindParam(":id", $id);
    $stmt->execute();
}
?>
