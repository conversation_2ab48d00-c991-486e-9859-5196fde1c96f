<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}
require_once(__DIR__ . "/../app/controllers/pedidosController.php");
$pedidos = getPedidos($_SESSION["empresa"]);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pedidos - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>

            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-shopping-cart"></i>
                            Pedidos
                        </h1>
                        <p>Gerencie todos os pedidos de vendas</p>
                    </div>
                    <div class="page-actions">
                        <a href="pedido_form.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Novo Pedido
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i>
                            Lista de Pedidos
                        </h3>
                        <div class="card-actions">
                            <span class="badge">
                                <?= count($pedidos) ?> pedidos
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pedidos)): ?>
                            <div class="empty-state">
                                <i class="fas fa-shopping-cart"></i>
                                <h3>Nenhum pedido encontrado</h3>
                                <p>Comece criando seu primeiro pedido</p>
                                <a href="pedido_form.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    Criar Primeiro Pedido
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>
                                                <i class="fas fa-hashtag"></i>
                                                ID
                                            </th>
                                            <th>
                                                <i class="fas fa-user"></i>
                                                Cliente
                                            </th>
                                            <th>
                                                <i class="fas fa-calendar"></i>
                                                Data
                                            </th>
                                            <th>
                                                <i class="fas fa-dollar-sign"></i>
                                                Total
                                            </th>
                                            <th>
                                                <i class="fas fa-cogs"></i>
                                                Ações
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pedidos as $p): ?>
                                        <tr>
                                            <td>
                                                <span class="badge badge-primary">
                                                    #<?= $p["id"] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="user-info">
                                                    <i class="fas fa-user-circle"></i>
                                                    <span><?= htmlspecialchars($p["cliente_nome"] ?? 'Cliente não encontrado') ?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="date-info">
                                                    <?php
                                                    $data = $p["data_pedido"] ?? $p["data"] ?? date('Y-m-d');
                                                    echo date('d/m/Y', strtotime($data));
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="price-tag">
                                                    R$ <?= number_format($p["total"] ?? 0, 2, ",", ".") ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="pedido_view.php?id=<?= $p["id"] ?>"
                                                       class="btn btn-sm btn-outline"
                                                       title="Visualizar">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="pedido_delete.php?id=<?= $p["id"] ?>"
                                                       class="btn btn-sm btn-danger"
                                                       onclick="return confirm('Tem certeza que deseja excluir este pedido?')"
                                                       title="Excluir">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--gray-700);
        }

        .empty-state p {
            margin-bottom: 30px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-info i {
            color: var(--gray-400);
        }

        .date-info {
            font-family: monospace;
            font-weight: 500;
        }

        .price-tag {
            font-weight: 700;
            color: var(--success);
            font-size: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .badge {
            background: var(--gray-100);
            color: var(--gray-700);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .badge-primary {
            background: var(--secondary);
            color: white;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
        }

        .table tbody tr:hover {
            background: var(--gray-50);
        }
    </style>
</body>
</html>
