<?php
require_once("../app/config/master.php");
require_once("../app/config/conexao.php");
session_start();

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = $_POST["email"];
    $senha = $_POST["senha"];

    $connMaster = getMasterConnection();
    $stmt = $connMaster->prepare("SELECT * FROM empresas WHERE ativa = TRUE");
    $stmt->execute();
    $empresas = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($empresas as $empresa) {
        $connEmpresa = getEmpresaConnection($empresa["banco_nome"]);
        $stmtUser = $connEmpresa->prepare("SELECT * FROM usuarios WHERE email = :email AND ativo = TRUE");
        $stmtUser->bindParam(":email", $email);
        $stmtUser->execute();
        $usuario = $stmtUser->fetch(PDO::FETCH_ASSOC);

        if ($usuario && password_verify($senha, $usuario["senha"])) {
            $_SESSION["empresa"] = $empresa["banco_nome"];
            $_SESSION["usuario"] = $usuario["nome"];
            header("Location: dashboard.php");
            exit;
        }
    }

    $erro = "Usuário ou senha inválidos!";
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary: #4a1a5c;
            --secondary: #ff6b35;
            --danger: #ef4444;
            --warning: #ffa726;
            --info: #42a5f5;
            --light: #f8fafc;
            --dark: #4a1a5c;
            --white: #ffffff;
            --shadow: 0 4px 6px rgba(74,26,92,0.15);
            --gradient-nextor: linear-gradient(135deg, #ffb347 0%, #ff6b35 25%, #ff4081 75%, #4a1a5c 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--gradient-nextor);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: var(--white);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--gradient-nextor);
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo i {
            font-size: 60px;
            color: var(--primary);
            margin-bottom: 10px;
        }

        .logo h1 {
            color: var(--primary);
            font-size: 28px;
            font-weight: 300;
            margin-bottom: 5px;
        }

        .logo p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .error-message {
            background: #fee;
            color: var(--danger);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid var(--danger);
            text-align: left;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark);
            font-weight: 500;
            font-size: 14px;
        }

        .input-group {
            position: relative;
        }

        .input-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 16px;
        }

        .form-control {
            width: 100%;
            padding: 15px 15px 15px 45px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary);
            background: var(--white);
            box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
        }

        .btn-login {
            width: 100%;
            padding: 15px;
            background: var(--gradient-nextor);
            color: var(--white);
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .footer-text {
            margin-top: 30px;
            color: #7f8c8d;
            font-size: 12px;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .logo h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-chart-line"></i>
            <h1>NEXTOR ERP</h1>
            <p>Sistema de Gestão Empresarial</p>
        </div>

        <?php if (!empty($erro)): ?>
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <?= $erro ?>
            </div>
        <?php endif; ?>

        <form method="POST">
            <div class="form-group">
                <label for="email">Email</label>
                <div class="input-group">
                    <i class="fas fa-envelope"></i>
                    <input type="email" id="email" name="email" class="form-control" placeholder="Digite seu email" required>
                </div>
            </div>

            <div class="form-group">
                <label for="senha">Senha</label>
                <div class="input-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" id="senha" name="senha" class="form-control" placeholder="Digite sua senha" required>
                </div>
            </div>

            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt"></i>
                Entrar no Sistema
            </button>
        </form>

        <div class="footer-text">
            © 2025 NEXTOR ERP - Todos os direitos reservados
        </div>
    </div>
</body>
</html>
