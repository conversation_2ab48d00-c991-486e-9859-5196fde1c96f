<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/financeiroController.php");

$edit = false;
$id = null;
$tipo = $descricao = $valor = $vencimento = $status = "";

if (isset($_GET["id"])) {
    $edit = true;
    $conn = getEmpresaConnection($_SESSION["empresa"]);
    $stmt = $conn->prepare("SELECT * FROM financeiro WHERE id = :id");
    $stmt->execute([":id" => $_GET["id"]]);
    $l = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($l) {
        $id = $l["id"];
        $tipo = $l["tipo"];
        $descricao = $l["descricao"];
        $valor = $l["valor"];
        $vencimento = $l["data_vencimento"];
        $status = $l["status"];
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $tipo = $_POST["tipo"];
    $descricao = $_POST["descricao"];
    $valor = $_POST["valor"];
    $vencimento = $_POST["vencimento"];
    $status = $_POST["status"];

    if ($edit) {
        updateLancamento($_SESSION["empresa"], $id, $tipo, $descricao, $valor, $vencimento, $status);
    } else {
        addLancamento($_SESSION["empresa"], $tipo, $descricao, $valor, $vencimento, $status);
    }
    header("Location: financeiro.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $edit ? "Editar Lançamento" : "Novo Lançamento" ?> - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>

            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-<?= $edit ? 'edit' : 'plus' ?>"></i>
                            <?= $edit ? "Editar Lançamento" : "Novo Lançamento" ?>
                        </h1>
                        <p>Preencha os dados do lançamento financeiro</p>
                    </div>
                    <div class="page-actions">
                        <a href="financeiro.php" class="btn btn-outline">
                            <i class="fas fa-arrow-left"></i>
                            Voltar
                        </a>
                    </div>
                </div>

                <div class="form-container">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-wallet"></i>
                                Dados do Lançamento
                            </h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="form-modern">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="tipo" class="form-label">
                                            <i class="fas fa-exchange-alt"></i>
                                            Tipo de Lançamento *
                                        </label>
                                        <select name="tipo" id="tipo" class="form-control form-select" required>
                                            <option value="receber" <?= $tipo == "receber" ? "selected" : "" ?>>
                                                💰 Conta a Receber
                                            </option>
                                            <option value="pagar" <?= $tipo == "pagar" ? "selected" : "" ?>>
                                                💸 Conta a Pagar
                                            </option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="status" class="form-label">
                                            <i class="fas fa-flag"></i>
                                            Status *
                                        </label>
                                        <select name="status" id="status" class="form-control form-select" required>
                                            <option value="pendente" <?= $status == "pendente" ? "selected" : "" ?>>
                                                ⏳ Pendente
                                            </option>
                                            <option value="pago" <?= $status == "pago" ? "selected" : "" ?>>
                                                ✅ Pago
                                            </option>
                                            <option value="cancelado" <?= $status == "cancelado" ? "selected" : "" ?>>
                                                ❌ Cancelado
                                            </option>
                                        </select>
                                    </div>

                                    <div class="form-group form-group-full">
                                        <label for="descricao" class="form-label">
                                            <i class="fas fa-file-alt"></i>
                                            Descrição *
                                        </label>
                                        <input
                                            type="text"
                                            id="descricao"
                                            name="descricao"
                                            class="form-control"
                                            value="<?= htmlspecialchars($descricao) ?>"
                                            placeholder="Digite a descrição do lançamento"
                                            required
                                        >
                                    </div>

                                    <div class="form-group">
                                        <label for="valor" class="form-label">
                                            <i class="fas fa-dollar-sign"></i>
                                            Valor (R$) *
                                        </label>
                                        <input
                                            type="number"
                                            id="valor"
                                            name="valor"
                                            class="form-control"
                                            value="<?= $valor ?>"
                                            step="0.01"
                                            min="0"
                                            placeholder="0,00"
                                            required
                                        >
                                    </div>

                                    <div class="form-group">
                                        <label for="vencimento" class="form-label">
                                            <i class="fas fa-calendar-alt"></i>
                                            Data de Vencimento
                                        </label>
                                        <input
                                            type="date"
                                            id="vencimento"
                                            name="vencimento"
                                            class="form-control"
                                            value="<?= $vencimento ?>"
                                        >
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        <?= $edit ? "Atualizar Lançamento" : "Cadastrar Lançamento" ?>
                                    </button>
                                    <a href="financeiro.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        Cancelar
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Formatação de valor
        document.getElementById('valor').addEventListener('input', function(e) {
            let value = e.target.value;
            if (value && !isNaN(value)) {
                e.target.value = parseFloat(value).toFixed(2);
            }
        });

        // Mudança de cor baseada no tipo
        document.getElementById('tipo').addEventListener('change', function() {
            const card = document.querySelector('.card');
            card.classList.remove('tipo-receber', 'tipo-pagar');

            if (this.value === 'receber') {
                card.classList.add('tipo-receber');
            } else if (this.value === 'pagar') {
                card.classList.add('tipo-pagar');
            }
        });

        // Aplicar classe inicial
        document.addEventListener('DOMContentLoaded', function() {
            const tipo = document.getElementById('tipo').value;
            const card = document.querySelector('.card');

            if (tipo === 'receber') {
                card.classList.add('tipo-receber');
            } else if (tipo === 'pagar') {
                card.classList.add('tipo-pagar');
            }
        });

        // Animação de foco nos inputs
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            // Se já tem valor, manter focado
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });
    </script>

    <style>
        .card.tipo-receber {
            border-left: 4px solid var(--success);
        }

        .card.tipo-pagar {
            border-left: 4px solid var(--danger);
        }

        .card.tipo-receber .card-header {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
        }

        .card.tipo-pagar .card-header {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
        }
    </style>
</body>
</html>
