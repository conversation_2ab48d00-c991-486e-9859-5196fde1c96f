<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/estoqueController.php");
require_once(__DIR__ . "/../app/controllers/produtosController.php");

$mensagem = '';
$erro = '';

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $produto_id = $_POST['produto_id'];
        $tipo = $_POST['tipo'];
        $quantidade = (int)$_POST['quantidade'];
        $motivo = $_POST['motivo'];
        $observacoes = $_POST['observacoes'] ?? '';
        $documento = $_POST['documento'] ?? '';
        $usuario = $_SESSION['usuario'] ?? 'Sistema';
        
        if ($quantidade <= 0) {
            throw new Exception("Quantidade deve ser maior que zero");
        }
        
        addMovimentacao($_SESSION["empresa"], $produto_id, $tipo, $quantidade, $motivo, $observacoes, $usuario, $documento);
        $mensagem = "Movimentação registrada com sucesso!";
        
    } catch (Exception $e) {
        $erro = $e->getMessage();
    }
}

// Buscar dados
$produtos = getProdutos($_SESSION["empresa"]);
$movimentacoes_recentes = getMovimentacoes($_SESSION["empresa"], 20);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Movimentação de Estoque - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>
            
            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-exchange-alt"></i>
                            Movimentação de Estoque
                        </h1>
                        <p>Registre entradas, saídas e ajustes de estoque</p>
                    </div>
                    <div class="page-actions">
                        <a href="estoque.php" class="btn btn-outline">
                            <i class="fas fa-arrow-left"></i>
                            Voltar ao Estoque
                        </a>
                    </div>
                </div>

                <?php if ($mensagem): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?= htmlspecialchars($mensagem) ?>
                    </div>
                <?php endif; ?>

                <?php if ($erro): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?= htmlspecialchars($erro) ?>
                    </div>
                <?php endif; ?>

                <div class="movimentacao-grid">
                    <!-- Formulário de Movimentação -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-plus-circle"></i>
                                Nova Movimentação
                            </h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="movimentacao-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="produto_id">Produto *</label>
                                        <select name="produto_id" id="produto_id" required class="form-control">
                                            <option value="">Selecione um produto</option>
                                            <?php foreach ($produtos as $produto): ?>
                                                <option value="<?= $produto['id'] ?>" data-estoque="<?= $produto['estoque'] ?>">
                                                    <?= htmlspecialchars($produto['descricao']) ?> 
                                                    (Estoque: <?= $produto['estoque'] ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="tipo">Tipo de Movimentação *</label>
                                        <select name="tipo" id="tipo" required class="form-control">
                                            <option value="">Selecione o tipo</option>
                                            <option value="entrada">📈 Entrada (Adicionar ao estoque)</option>
                                            <option value="saida">📉 Saída (Remover do estoque)</option>
                                            <option value="ajuste">⚖️ Ajuste (Corrigir estoque)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="quantidade">Quantidade *</label>
                                        <input type="number" name="quantidade" id="quantidade" required min="1" class="form-control" placeholder="Digite a quantidade">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="motivo">Motivo *</label>
                                        <select name="motivo" id="motivo" required class="form-control">
                                            <option value="">Selecione o motivo</option>
                                            <!-- Opções serão preenchidas via JavaScript baseado no tipo -->
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="documento">Documento de Referência</label>
                                    <input type="text" name="documento" id="documento" class="form-control" placeholder="Ex: NF-001, PED-123, etc.">
                                </div>

                                <div class="form-group">
                                    <label for="observacoes">Observações</label>
                                    <textarea name="observacoes" id="observacoes" class="form-control" rows="3" placeholder="Informações adicionais sobre a movimentação"></textarea>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        Registrar Movimentação
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i>
                                        Limpar
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Movimentações Recentes -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-history"></i>
                                Movimentações Recentes
                            </h3>
                            <div class="card-actions">
                                <span class="badge"><?= count($movimentacoes_recentes) ?> registros</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($movimentacoes_recentes)): ?>
                                <div class="empty-state-mini">
                                    <i class="fas fa-clipboard-list"></i>
                                    <p>Nenhuma movimentação registrada</p>
                                </div>
                            <?php else: ?>
                                <div class="movimentacoes-lista">
                                    <?php foreach ($movimentacoes_recentes as $mov): ?>
                                        <div class="movimentacao-item">
                                            <div class="mov-icon <?= $mov['tipo'] ?>">
                                                <i class="fas fa-<?= $mov['tipo'] == 'entrada' ? 'arrow-up' : ($mov['tipo'] == 'saida' ? 'arrow-down' : 'edit') ?>"></i>
                                            </div>
                                            <div class="mov-info">
                                                <h4><?= htmlspecialchars($mov['produto_nome']) ?></h4>
                                                <div class="mov-detalhes">
                                                    <span class="mov-tipo"><?= ucfirst($mov['tipo']) ?></span>
                                                    <span class="mov-quantidade"><?= abs($mov['quantidade']) ?> unidades</span>
                                                    <span class="mov-motivo"><?= htmlspecialchars($mov['motivo']) ?></span>
                                                </div>
                                                <div class="mov-meta">
                                                    <span class="mov-data"><?= date('d/m/Y H:i', strtotime($mov['data_movimentacao'])) ?></span>
                                                    <?php if ($mov['documento']): ?>
                                                        <span class="mov-doc">Doc: <?= htmlspecialchars($mov['documento']) ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if ($mov['observacoes']): ?>
                                                    <div class="mov-obs">
                                                        <i class="fas fa-comment"></i>
                                                        <?= htmlspecialchars($mov['observacoes']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="mov-estoque">
                                                <span class="estoque-anterior"><?= $mov['quantidade_anterior'] ?></span>
                                                <i class="fas fa-arrow-right"></i>
                                                <span class="estoque-atual"><?= $mov['quantidade_atual'] ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Atualizar motivos baseado no tipo selecionado
        document.getElementById('tipo').addEventListener('change', function() {
            const motivo = document.getElementById('motivo');
            const tipo = this.value;
            
            motivo.innerHTML = '<option value="">Selecione o motivo</option>';
            
            let opcoes = [];
            
            if (tipo === 'entrada') {
                opcoes = [
                    'compra',
                    'devolucao_cliente',
                    'transferencia_entrada',
                    'producao',
                    'ajuste_positivo',
                    'outros'
                ];
            } else if (tipo === 'saida') {
                opcoes = [
                    'venda',
                    'devolucao_fornecedor',
                    'transferencia_saida',
                    'perda',
                    'quebra',
                    'uso_interno',
                    'ajuste_negativo',
                    'outros'
                ];
            } else if (tipo === 'ajuste') {
                opcoes = [
                    'inventario',
                    'correcao_sistema',
                    'auditoria',
                    'outros'
                ];
            }
            
            opcoes.forEach(opcao => {
                const option = document.createElement('option');
                option.value = opcao;
                option.textContent = opcao.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
                motivo.appendChild(option);
            });
        });

        // Mostrar estoque atual do produto selecionado
        document.getElementById('produto_id').addEventListener('change', function() {
            const option = this.options[this.selectedIndex];
            const estoque = option.getAttribute('data-estoque');
            
            if (estoque) {
                console.log('Estoque atual:', estoque);
            }
        });
    </script>
</body>
</html>
