<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/produtosController.php");

$edit = false;
$descricao = $preco = $estoque = "";
$id = null;

if (isset($_GET["id"])) {
    $edit = true;
    $conn = getEmpresaConnection($_SESSION["empresa"]);
    $stmt = $conn->prepare("SELECT * FROM produtos WHERE id = :id");
    $stmt->bindParam(":id", $_GET["id"]);
    $stmt->execute();
    $produto = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($produto) {
        $id = $produto["id"];
        $descricao = $produto["descricao"];
        $preco = $produto["preco"];
        $estoque = $produto["estoque"];
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $descricao = $_POST["descricao"];
    $preco = $_POST["preco"];
    $estoque = $_POST["estoque"];

    if ($edit) {
        updateProduto($_SESSION["empresa"], $id, $descricao, $preco, $estoque);
    } else {
        addProduto($_SESSION["empresa"], $descricao, $preco, $estoque);
    }
    header("Location: produtos.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $edit ? "Editar Produto" : "Novo Produto" ?> - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>

            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-<?= $edit ? 'edit' : 'plus' ?>"></i>
                            <?= $edit ? "Editar Produto" : "Novo Produto" ?>
                        </h1>
                        <p>Preencha as informações do produto</p>
                    </div>
                    <div class="page-actions">
                        <a href="produtos.php" class="btn btn-outline">
                            <i class="fas fa-arrow-left"></i>
                            Voltar
                        </a>
                    </div>
                </div>

                <div class="form-container">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-box"></i>
                                Dados do Produto
                            </h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="form-modern">
                                <div class="form-grid">
                                    <div class="form-group form-group-full">
                                        <label for="descricao" class="form-label">
                                            <i class="fas fa-tag"></i>
                                            Descrição do Produto *
                                        </label>
                                        <input
                                            type="text"
                                            id="descricao"
                                            name="descricao"
                                            class="form-control"
                                            value="<?= htmlspecialchars($descricao) ?>"
                                            placeholder="Digite a descrição do produto"
                                            required
                                        >
                                    </div>

                                    <div class="form-group">
                                        <label for="preco" class="form-label">
                                            <i class="fas fa-dollar-sign"></i>
                                            Preço (R$) *
                                        </label>
                                        <input
                                            type="number"
                                            id="preco"
                                            name="preco"
                                            class="form-control"
                                            value="<?= $preco ?>"
                                            step="0.01"
                                            min="0"
                                            placeholder="0,00"
                                            required
                                        >
                                    </div>

                                    <div class="form-group">
                                        <label for="estoque" class="form-label">
                                            <i class="fas fa-cubes"></i>
                                            Estoque Inicial
                                        </label>
                                        <input
                                            type="number"
                                            id="estoque"
                                            name="estoque"
                                            class="form-control"
                                            value="<?= $estoque ?>"
                                            min="0"
                                            placeholder="0"
                                        >
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        <?= $edit ? "Atualizar Produto" : "Cadastrar Produto" ?>
                                    </button>
                                    <a href="produtos.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        Cancelar
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Formatação de preço
        document.getElementById('preco').addEventListener('input', function(e) {
            let value = e.target.value;
            if (value && !isNaN(value)) {
                e.target.value = parseFloat(value).toFixed(2);
            }
        });

        // Animação de foco nos inputs
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            // Se já tem valor, manter focado
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });
    </script>
</body>
</html>
