<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}
require_once(__DIR__ . "/../app/controllers/produtosController.php");
$produtos = getProdutos($_SESSION["empresa"]);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produtos - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>

            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-box"></i>
                            Produtos
                        </h1>
                        <p>Gerencie o catálogo de produtos da empresa</p>
                    </div>
                    <div class="page-actions">
                        <a href="produto_form.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Novo Produto
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i>
                            Catálogo de Produtos
                        </h3>
                        <div class="card-actions">
                            <span class="badge">
                                <?= count($produtos) ?> produtos
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="produtos-grid">
                        <?php if (empty($produtos)): ?>
                            <div class="empty-state">
                                <i class="fas fa-box"></i>
                                <h3>Nenhum produto encontrado</h3>
                                <p>Comece criando seu primeiro produto</p>
                                <a href="produto_form.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    Criar Primeiro Produto
                                </a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($produtos as $p): ?>
                                <div class="produto-card">
                                    <h3><i class="fas fa-cube"></i> <?= htmlspecialchars($p["descricao"]) ?></h3>
                                    <p class="produto-preco">R$ <?= number_format($p["preco"], 2, ",", ".") ?></p>

                                    <?php
                                        $estoque = $p["estoque"];
                                        $estoqueClass = "estoque-alto";
                                        $estoqueLabel = "Estoque Alto";
                                        if ($estoque <= 5) {
                                            $estoqueClass = "estoque-baixo";
                                            $estoqueLabel = "Estoque Baixo";
                                        } elseif ($estoque <= 20) {
                                            $estoqueClass = "estoque-medio";
                                            $estoqueLabel = "Estoque Médio";
                                        }
                                    ?>
                                    <span class="estoque <?= $estoqueClass ?>">📦 <?= $estoqueLabel ?> (<?= $estoque ?>)</span>

                                    <div class="card-actions">
                                        <a href="produto_form.php?id=<?= $p["id"] ?>" class="btn btn-outline"><i class="fas fa-edit"></i> Editar</a>
                                        <a href="produto_delete.php?id=<?= $p["id"] ?>" class="btn btn-danger" onclick="return confirm('Excluir produto?')"><i class="fas fa-trash"></i> Excluir</a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
