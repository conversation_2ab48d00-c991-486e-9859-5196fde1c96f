<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/pedidosController.php");

$pedido = getPedido($_SESSION["empresa"], $_GET["id"]);
$itens = getItensPedido($_SESSION["empresa"], $_GET["id"]);

// Buscar dados do cliente
require_once(__DIR__ . "/../app/config/conexao.php");
$conn = getEmpresaConnection($_SESSION["empresa"]);
$stmt = $conn->prepare("SELECT nome, telefone, email, endereco FROM clientes WHERE id = ?");
$stmt->execute([$pedido["cliente_id"]]);
$cliente = $stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pedido #<?= $pedido["id"] ?> - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>

            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-file-invoice"></i>
                            Pedido #<?= $pedido["id"] ?>
                        </h1>
                        <p>Detalhes completos do pedido de venda</p>
                    </div>
                    <div class="page-actions">
                        <a href="pedidos.php" class="btn btn-outline">
                            <i class="fas fa-arrow-left"></i>
                            Voltar
                        </a>
                        <button onclick="window.print()" class="btn btn-secondary">
                            <i class="fas fa-print"></i>
                            Imprimir
                        </button>
                    </div>
                </div>

                <div class="pedido-container">
                    <!-- Cabeçalho do Pedido -->
                    <div class="card pedido-header">
                        <div class="card-body">
                            <div class="pedido-info-grid">
                                <div class="pedido-numero">
                                    <h2>
                                        <i class="fas fa-hashtag"></i>
                                        Pedido #<?= $pedido["id"] ?>
                                    </h2>
                                    <span class="status-badge status-<?= $pedido["status"] ?? 'pendente' ?>">
                                        <?= ucfirst($pedido["status"] ?? 'Pendente') ?>
                                    </span>
                                </div>
                                <div class="pedido-data">
                                    <div class="info-item">
                                        <i class="fas fa-calendar"></i>
                                        <span class="label">Data do Pedido:</span>
                                        <span class="value">
                                            <?= date('d/m/Y', strtotime($pedido["data_pedido"] ?? $pedido["data"] ?? date('Y-m-d'))) ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span class="label">Total:</span>
                                        <span class="value total-value">
                                            R$ <?= number_format($pedido["total"], 2, ',', '.') ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dados do Cliente -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user"></i>
                                Dados do Cliente
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="cliente-info">
                                <div class="cliente-principal">
                                    <h4><?= htmlspecialchars($cliente["nome"] ?? 'Cliente não encontrado') ?></h4>
                                    <div class="cliente-contatos">
                                        <?php if (!empty($cliente["telefone"])): ?>
                                        <div class="contato-item">
                                            <i class="fas fa-phone"></i>
                                            <span><?= htmlspecialchars($cliente["telefone"]) ?></span>
                                        </div>
                                        <?php endif; ?>

                                        <?php if (!empty($cliente["email"])): ?>
                                        <div class="contato-item">
                                            <i class="fas fa-envelope"></i>
                                            <span><?= htmlspecialchars($cliente["email"]) ?></span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <?php if (!empty($cliente["endereco"])): ?>
                                <div class="cliente-endereco">
                                    <div class="endereco-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span><?= htmlspecialchars($cliente["endereco"]) ?></span>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Itens do Pedido -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-box"></i>
                                Itens do Pedido
                            </h3>
                            <div class="card-actions">
                                <span class="badge">
                                    <?= count($itens) ?> itens
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table pedido-itens-table">
                                    <thead>
                                        <tr>
                                            <th>Produto</th>
                                            <th>Preço Unit.</th>
                                            <th>Quantidade</th>
                                            <th>Subtotal</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $totalGeral = 0;
                                        foreach ($itens as $i):
                                            $preco = $i["preco"] ?? $i["preco_unitario"] ?? 0;
                                            $subtotal = $preco * $i["quantidade"];
                                            $totalGeral += $subtotal;
                                        ?>
                                        <tr>
                                            <td>
                                                <div class="produto-info">
                                                    <i class="fas fa-cube"></i>
                                                    <span><?= htmlspecialchars($i["descricao"]) ?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="preco-unitario">
                                                    R$ <?= number_format($preco, 2, ',', '.') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="quantidade">
                                                    <?= $i["quantidade"] ?>x
                                                </span>
                                            </td>
                                            <td>
                                                <span class="subtotal">
                                                    R$ <?= number_format($subtotal, 2, ',', '.') ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class="total-row">
                                            <td colspan="3">
                                                <strong>Total do Pedido:</strong>
                                            </td>
                                            <td>
                                                <strong class="total-final">
                                                    R$ <?= number_format($totalGeral, 2, ',', '.') ?>
                                                </strong>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .pedido-container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .pedido-header .card-body {
            padding: 30px;
        }

        .pedido-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: center;
        }

        .pedido-numero h2 {
            margin: 0 0 10px 0;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendente {
            background: rgba(251, 191, 36, 0.1);
            color: #f59e0b;
        }

        .status-pago {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .info-item i {
            color: var(--secondary);
            width: 16px;
        }

        .info-item .label {
            font-weight: 500;
            color: var(--gray-600);
        }

        .info-item .value {
            font-weight: 600;
            color: var(--gray-900);
        }

        .total-value {
            font-size: 24px;
            color: var(--success) !important;
        }

        .cliente-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .cliente-principal h4 {
            margin: 0 0 15px 0;
            color: var(--gray-900);
            font-size: 20px;
        }

        .contato-item,
        .endereco-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .contato-item i,
        .endereco-item i {
            color: var(--secondary);
            width: 16px;
        }

        .pedido-itens-table {
            width: 100%;
            border-collapse: collapse;
        }

        .pedido-itens-table th,
        .pedido-itens-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .pedido-itens-table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
        }

        .produto-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .produto-info i {
            color: var(--gray-400);
        }

        .preco-unitario,
        .subtotal {
            font-weight: 600;
            color: var(--gray-700);
        }

        .quantidade {
            font-weight: 600;
            color: var(--secondary);
        }

        .total-row {
            background: var(--gray-50);
        }

        .total-final {
            font-size: 18px;
            color: var(--success);
        }

        @media print {
            .wrapper .sidebar,
            .page-header .page-actions,
            .btn {
                display: none !important;
            }

            .main {
                margin-left: 0 !important;
            }

            .container {
                max-width: none !important;
                padding: 0 !important;
            }
        }

        @media (max-width: 768px) {
            .pedido-info-grid,
            .cliente-info {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .page-actions {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</body>
</html>
