<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/pedidosController.php");
require_once(__DIR__ . "/../app/config/conexao.php");

$conn = getEmpresaConnection($_SESSION["empresa"]);
$clientes = $conn->query("SELECT * FROM clientes ORDER BY nome")->fetchAll(PDO::FETCH_ASSOC);
$produtos = $conn->query("SELECT * FROM produtos ORDER BY descricao")->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $cliente_id = $_POST["cliente_id"];
    $itens = [];
    foreach ($_POST["produto_id"] as $index => $produto_id) {
        $quantidade = $_POST["quantidade"][$index];
        $preco = $_POST["preco"][$index];
        if ($quantidade > 0) {
            $itens[] = [
                "produto_id" => $produto_id,
                "quantidade" => $quantidade,
                "preco" => $preco
            ];
        }
    }
    $pedido_id = addPedido($_SESSION["empresa"], $cliente_id, $itens);
    header("Location: pedido_view.php?id=" . $pedido_id);
    exit;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novo Pedido - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>

            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-shopping-cart"></i>
                            Novo Pedido
                        </h1>
                        <p>Selecione o cliente e adicione os produtos ao pedido</p>
                    </div>
                    <div class="page-actions">
                        <a href="pedidos.php" class="btn btn-outline">
                            <i class="fas fa-arrow-left"></i>
                            Voltar
                        </a>
                    </div>
                </div>

                <form method="POST" class="form-modern">
                    <!-- Seleção do Cliente -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user"></i>
                                Cliente
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="cliente_id" class="form-label">
                                    <i class="fas fa-user"></i>
                                    Selecione o Cliente *
                                </label>
                                <select name="cliente_id" id="cliente_id" class="form-control form-select" required>
                                    <option value="">-- Selecione um cliente --</option>
                                    <?php foreach ($clientes as $c): ?>
                                        <option value="<?= $c["id"] ?>"><?= htmlspecialchars($c["nome"]) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Produtos -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-box"></i>
                                Produtos
                            </h3>
                            <div class="card-actions">
                                <span class="total-display">Total: R$ <span id="total-pedido">0,00</span></span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="produtos-grid">
                                <?php foreach ($produtos as $p): ?>
                                <div class="produto-item" data-preco="<?= $p["preco"] ?>">
                                    <div class="produto-info">
                                        <h4 class="produto-nome"><?= htmlspecialchars($p["descricao"]) ?></h4>
                                        <div class="produto-preco">R$ <?= number_format($p["preco"], 2, ',', '.') ?></div>
                                        <div class="produto-estoque">Estoque: <?= $p["estoque"] ?> unidades</div>
                                    </div>
                                    <div class="produto-controls">
                                        <label for="qtd_<?= $p["id"] ?>" class="form-label">
                                            <i class="fas fa-sort-numeric-up"></i>
                                            Quantidade
                                        </label>
                                        <input
                                            type="number"
                                            id="qtd_<?= $p["id"] ?>"
                                            name="quantidade[]"
                                            class="form-control quantidade-input"
                                            value="0"
                                            min="0"
                                            max="<?= $p["estoque"] ?>"
                                            data-preco="<?= $p["preco"] ?>"
                                        >
                                        <input type="hidden" name="produto_id[]" value="<?= $p["id"] ?>">
                                        <input type="hidden" name="preco[]" value="<?= $p["preco"] ?>">
                                    </div>
                                    <div class="produto-subtotal">
                                        Subtotal: R$ <span class="subtotal-valor">0,00</span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Resumo e Ações -->
                    <div class="card">
                        <div class="card-body">
                            <div class="pedido-resumo">
                                <div class="resumo-item">
                                    <span class="resumo-label">Total de Itens:</span>
                                    <span class="resumo-valor" id="total-itens">0</span>
                                </div>
                                <div class="resumo-item total">
                                    <span class="resumo-label">Total do Pedido:</span>
                                    <span class="resumo-valor" id="total-final">R$ 0,00</span>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary" id="btn-salvar" disabled>
                                    <i class="fas fa-save"></i>
                                    Salvar Pedido
                                </button>
                                <a href="pedidos.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    Cancelar
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <style>
        .produtos-grid {
            display: grid;
            gap: 20px;
        }

        .produto-item {
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 20px;
            transition: var(--transition);
        }

        .produto-item:hover {
            border-color: var(--secondary);
            box-shadow: var(--shadow);
        }

        .produto-item.selected {
            border-color: var(--secondary);
            background: rgba(255, 107, 53, 0.05);
        }

        .produto-info {
            margin-bottom: 16px;
        }

        .produto-nome {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 8px;
        }

        .produto-preco {
            font-size: 20px;
            font-weight: 700;
            color: var(--secondary);
            margin-bottom: 4px;
        }

        .produto-estoque {
            font-size: 14px;
            color: var(--gray-600);
        }

        .produto-controls {
            margin-bottom: 12px;
        }

        .produto-subtotal {
            font-weight: 600;
            color: var(--gray-700);
            text-align: right;
        }

        .pedido-resumo {
            background: var(--gray-50);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 24px;
        }

        .resumo-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .resumo-item.total {
            border-top: 1px solid var(--gray-300);
            padding-top: 12px;
            margin-top: 12px;
            font-size: 18px;
            font-weight: 700;
        }

        .total-display {
            font-size: 18px;
            font-weight: 700;
            color: var(--secondary);
        }
    </style>

    <script>
        function calcularTotais() {
            let totalPedido = 0;
            let totalItens = 0;

            document.querySelectorAll('.quantidade-input').forEach(input => {
                const quantidade = parseInt(input.value) || 0;
                const preco = parseFloat(input.dataset.preco) || 0;
                const subtotal = quantidade * preco;

                // Atualizar subtotal do produto
                const subtotalElement = input.closest('.produto-item').querySelector('.subtotal-valor');
                subtotalElement.textContent = subtotal.toLocaleString('pt-BR', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });

                // Marcar produto como selecionado
                if (quantidade > 0) {
                    input.closest('.produto-item').classList.add('selected');
                    totalItens += quantidade;
                } else {
                    input.closest('.produto-item').classList.remove('selected');
                }

                totalPedido += subtotal;
            });

            // Atualizar totais
            document.getElementById('total-pedido').textContent = totalPedido.toLocaleString('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });

            document.getElementById('total-final').textContent = 'R$ ' + totalPedido.toLocaleString('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });

            document.getElementById('total-itens').textContent = totalItens;

            // Habilitar/desabilitar botão salvar
            const btnSalvar = document.getElementById('btn-salvar');
            const clienteSelecionado = document.getElementById('cliente_id').value;
            btnSalvar.disabled = !(totalItens > 0 && clienteSelecionado);
        }

        // Event listeners
        document.querySelectorAll('.quantidade-input').forEach(input => {
            input.addEventListener('input', calcularTotais);
        });

        document.getElementById('cliente_id').addEventListener('change', calcularTotais);

        // Calcular totais inicial
        calcularTotais();
    </script>
</body>
</html>
