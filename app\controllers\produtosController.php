<?php
require_once(__DIR__ . "/../config/conexao.php");
require_once(__DIR__ . "/../models/Produto.php");

function getProdutos($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->query("SELECT * FROM produtos ORDER BY id DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addProduto($empresaBanco, $descricao, $preco, $estoque) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("INSERT INTO produtos (descricao, preco, estoque) VALUES (:descricao, :preco, :estoque)");
    $stmt->bindParam(":descricao", $descricao);
    $stmt->bindParam(":preco", $preco);
    $stmt->bindParam(":estoque", $estoque);
    $stmt->execute();
}

function updateProduto($empresaBanco, $id, $descricao, $preco, $estoque) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("UPDATE produtos SET descricao = :descricao, preco = :preco, estoque = :estoque WHERE id = :id");
    $stmt->bindParam(":descricao", $descricao);
    $stmt->bindParam(":preco", $preco);
    $stmt->bindParam(":estoque", $estoque);
    $stmt->bindParam(":id", $id);
    $stmt->execute();
}

function deleteProduto($empresaBanco, $id) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("DELETE FROM produtos WHERE id = :id");
    $stmt->bindParam(":id", $id);
    $stmt->execute();
}
?>
