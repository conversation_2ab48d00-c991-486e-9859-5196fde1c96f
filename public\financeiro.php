<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}
require_once(__DIR__ . "/../app/controllers/financeiroController.php");
$lancamentos = getLancamentos($_SESSION["empresa"]);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financeiro - NEXTOR ERP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="wrapper">
        <?php include "layout/sidebar.php"; ?>
        <div class="main">
            <?php include "layout/header.php"; ?>

            <div class="container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <i class="fas fa-wallet"></i>
                            Financeiro
                        </h1>
                        <p>Controle de contas a pagar e receber</p>
                    </div>
                    <div class="page-actions">
                        <a href="financeiro_form.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Novo Lançamento
                        </a>
                    </div>
                </div>

                <!-- Resumo Financeiro -->
                <?php
                $totalReceber = 0;
                $totalPagar = 0;
                $totalReceberPago = 0;
                $totalReceberPendente = 0;
                $totalReceberCancelado = 0;
                $totalPagarPago = 0;
                $totalPagarPendente = 0;
                $totalPagarCancelado = 0;

                foreach ($lancamentos as $l) {
                    if ($l["tipo"] == "receber") {
                        $totalReceber += $l["valor"];
                        switch ($l["status"]) {
                            case "pago":
                                $totalReceberPago += $l["valor"];
                                break;
                            case "pendente":
                                $totalReceberPendente += $l["valor"];
                                break;
                            case "cancelado":
                                $totalReceberCancelado += $l["valor"];
                                break;
                        }
                    } else {
                        $totalPagar += $l["valor"];
                        switch ($l["status"]) {
                            case "pago":
                                $totalPagarPago += $l["valor"];
                                break;
                            case "pendente":
                                $totalPagarPendente += $l["valor"];
                                break;
                            case "cancelado":
                                $totalPagarCancelado += $l["valor"];
                                break;
                        }
                    }
                }

                // Saldo real (apenas valores pagos)
                $saldoReal = $totalReceberPago - $totalPagarPago;
                // Saldo projetado (incluindo pendentes, excluindo cancelados)
                $saldoProjetado = ($totalReceberPago + $totalReceberPendente) - ($totalPagarPago + $totalPagarPendente);
                ?>

                <div class="financeiro-resumo">
                    <div class="resumo-card receitas">
                        <div class="resumo-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="resumo-info">
                            <h3>Receitas</h3>
                            <div class="resumo-valor">R$ <?= number_format($totalReceber, 2, ',', '.') ?></div>
                            <div class="resumo-detalhes">
                                <div class="status-item pago">
                                    <i class="fas fa-check"></i>
                                    Recebido: R$ <?= number_format($totalReceberPago, 2, ',', '.') ?>
                                </div>
                                <div class="status-item pendente">
                                    <i class="fas fa-clock"></i>
                                    Pendente: R$ <?= number_format($totalReceberPendente, 2, ',', '.') ?>
                                </div>
                                <?php if ($totalReceberCancelado > 0): ?>
                                <div class="status-item cancelado">
                                    <i class="fas fa-times"></i>
                                    Cancelado: R$ <?= number_format($totalReceberCancelado, 2, ',', '.') ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="resumo-card despesas">
                        <div class="resumo-icon">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="resumo-info">
                            <h3>Despesas</h3>
                            <div class="resumo-valor">R$ <?= number_format($totalPagar, 2, ',', '.') ?></div>
                            <div class="resumo-detalhes">
                                <div class="status-item pago">
                                    <i class="fas fa-check"></i>
                                    Pago: R$ <?= number_format($totalPagarPago, 2, ',', '.') ?>
                                </div>
                                <div class="status-item pendente">
                                    <i class="fas fa-clock"></i>
                                    Pendente: R$ <?= number_format($totalPagarPendente, 2, ',', '.') ?>
                                </div>
                                <?php if ($totalPagarCancelado > 0): ?>
                                <div class="status-item cancelado">
                                    <i class="fas fa-times"></i>
                                    Cancelado: R$ <?= number_format($totalPagarCancelado, 2, ',', '.') ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="resumo-card saldo-container">
                        <div class="saldo-item saldo-real <?= $saldoReal >= 0 ? 'positivo' : 'negativo' ?>">
                            <div class="saldo-header">
                                <div class="resumo-icon">
                                    <i class="fas fa-wallet"></i>
                                </div>
                                <div class="saldo-info">
                                    <h3>Saldo Real</h3>
                                    <div class="resumo-valor">R$ <?= number_format($saldoReal, 2, ',', '.') ?></div>
                                    <div class="resumo-status">Apenas valores pagos</div>
                                </div>
                            </div>
                        </div>

                        <div class="saldo-item saldo-projetado <?= $saldoProjetado >= 0 ? 'positivo' : 'negativo' ?>">
                            <div class="saldo-header">
                                <div class="resumo-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="saldo-info">
                                    <h3>Saldo Projetado</h3>
                                    <div class="resumo-valor">R$ <?= number_format($saldoProjetado, 2, ',', '.') ?></div>
                                    <div class="resumo-status">Incluindo pendentes</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i>
                            Lançamentos Financeiros
                        </h3>
                        <div class="card-actions">
                            <span class="badge">
                                <?= count($lancamentos) ?> lançamentos
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($lancamentos)): ?>
                            <div class="empty-state">
                                <i class="fas fa-wallet"></i>
                                <h3>Nenhum lançamento encontrado</h3>
                                <p>Comece criando seu primeiro lançamento financeiro</p>
                                <a href="financeiro_form.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    Criar Primeiro Lançamento
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>
                                                <i class="fas fa-hashtag"></i>
                                                ID
                                            </th>
                                            <th>
                                                <i class="fas fa-exchange-alt"></i>
                                                Tipo
                                            </th>
                                            <th>
                                                <i class="fas fa-file-alt"></i>
                                                Descrição
                                            </th>
                                            <th>
                                                <i class="fas fa-dollar-sign"></i>
                                                Valor
                                            </th>
                                            <th>
                                                <i class="fas fa-calendar"></i>
                                                Vencimento
                                            </th>
                                            <th>
                                                <i class="fas fa-flag"></i>
                                                Status
                                            </th>
                                            <th>
                                                <i class="fas fa-cogs"></i>
                                                Ações
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($lancamentos as $l): ?>
                                        <tr class="lancamento-row lancamento-<?= $l["tipo"] ?>">
                                            <td>
                                                <span class="badge badge-primary">
                                                    #<?= $l["id"] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="tipo-badge tipo-<?= $l["tipo"] ?>">
                                                    <?php if ($l["tipo"] == "receber"): ?>
                                                        <i class="fas fa-arrow-up"></i>
                                                        Receber
                                                    <?php else: ?>
                                                        <i class="fas fa-arrow-down"></i>
                                                        Pagar
                                                    <?php endif; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="descricao-info">
                                                    <?= htmlspecialchars($l["descricao"]) ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="valor-<?= $l["tipo"] ?>">
                                                    R$ <?= number_format($l["valor"], 2, ",", ".") ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="date-info">
                                                    <?= date('d/m/Y', strtotime($l["data_vencimento"] ?? $l["vencimento"] ?? date('Y-m-d'))) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?= $l["status"] ?>">
                                                    <?php
                                                    switch($l["status"]) {
                                                        case 'pago':
                                                            echo '<i class="fas fa-check"></i> Pago';
                                                            break;
                                                        case 'pendente':
                                                            echo '<i class="fas fa-clock"></i> Pendente';
                                                            break;
                                                        case 'cancelado':
                                                            echo '<i class="fas fa-times"></i> Cancelado';
                                                            break;
                                                        default:
                                                            echo '<i class="fas fa-question"></i> ' . ucfirst($l["status"]);
                                                    }
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="financeiro_form.php?id=<?= $l["id"] ?>"
                                                       class="btn btn-sm btn-outline"
                                                       title="Editar">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="financeiro_delete.php?id=<?= $l["id"] ?>"
                                                       class="btn btn-sm btn-danger"
                                                       onclick="return confirm('Tem certeza que deseja excluir este lançamento?')"
                                                       title="Excluir">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .financeiro-resumo {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        @media (max-width: 1024px) {
            .financeiro-resumo {
                grid-template-columns: 1fr;
            }
        }

        .resumo-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 24px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            gap: 16px;
            border-left: 4px solid;
        }

        .resumo-card.receitas {
            border-left-color: var(--success);
        }

        .resumo-card.despesas {
            border-left-color: var(--danger);
        }

        .resumo-card.saldo.positivo {
            border-left-color: var(--success);
        }

        .resumo-card.saldo.negativo {
            border-left-color: var(--danger);
        }

        .resumo-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .receitas .resumo-icon {
            background: var(--success);
        }

        .despesas .resumo-icon {
            background: var(--danger);
        }

        .saldo.positivo .resumo-icon {
            background: var(--success);
        }

        .saldo.negativo .resumo-icon {
            background: var(--danger);
        }

        .resumo-info h3 {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: var(--gray-600);
            text-transform: uppercase;
            font-weight: 600;
        }

        .resumo-valor {
            font-size: 24px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 4px;
        }

        .resumo-pendente,
        .resumo-status {
            font-size: 12px;
            color: var(--gray-500);
        }

        .resumo-detalhes {
            margin-top: 12px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            margin-bottom: 4px;
        }

        .status-item.pago {
            color: var(--success);
        }

        .status-item.pendente {
            color: #f59e0b;
        }

        .status-item.cancelado {
            color: var(--gray-500);
        }

        .saldo-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .saldo-item {
            background: white;
            border-radius: var(--border-radius);
            padding: 16px;
            box-shadow: var(--shadow-sm);
            border-left: 4px solid;
        }

        .saldo-item.positivo {
            border-left-color: var(--success);
        }

        .saldo-item.negativo {
            border-left-color: var(--danger);
        }

        .saldo-header {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .saldo-item .resumo-icon {
            width: 36px;
            height: 36px;
            font-size: 16px;
        }

        .saldo-item .resumo-valor {
            font-size: 18px;
            margin-bottom: 2px;
        }

        .saldo-item h3 {
            font-size: 12px;
            margin-bottom: 6px;
        }

        .tipo-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .tipo-receber {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .tipo-pagar {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }

        .valor-receber {
            color: var(--success);
            font-weight: 600;
        }

        .valor-pagar {
            color: var(--danger);
            font-weight: 600;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-pago {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .status-pendente {
            background: rgba(251, 191, 36, 0.1);
            color: #f59e0b;
        }

        .status-cancelado {
            background: rgba(107, 114, 128, 0.1);
            color: var(--gray-600);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--gray-700);
        }

        .empty-state p {
            margin-bottom: 30px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
        }

        .table tbody tr:hover {
            background: var(--gray-50);
        }

        .badge {
            background: var(--gray-100);
            color: var(--gray-700);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .badge-primary {
            background: var(--secondary);
            color: white;
        }

        .date-info {
            font-family: monospace;
            font-weight: 500;
        }
    </style>
</body>
</html>
