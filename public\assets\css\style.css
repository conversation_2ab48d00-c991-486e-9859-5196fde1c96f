/* <PERSON><PERSON><PERSON><PERSON><PERSON> CSS - Design Moderno NEXTOR */
:root {
    --primary: #4a1a5c;
    --primary-light: #6b2c7a;
    --secondary: #ff6b35;
    --secondary-light: #ff8a5c;
    --accent: #ff4081;
    --success: #10b981;
    --danger: #ef4444;
    --warning: #ffa726;
    --info: #42a5f5;
    --light: #f8fafc;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --gradient-primary: linear-gradient(135deg, #ff6b35 0%, #ff4081 50%, #4a1a5c 100%);
    --gradient-secondary: linear-gradient(135deg, #ffa726 0%, #ff6b35 100%);
    --gradient-success: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    --gradient-nextor: linear-gradient(135deg, #ffb347 0%, #ff6b35 25%, #ff4081 75%, #4a1a5c 100%);
    --gradient-nextor-light: linear-gradient(135deg, #ffd4a3 0%, #ffb3a7 50%, #c39bd3 100%);
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset e base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    display: flex;
    min-height: 100vh;
    font-size: 14px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Importar fonte Inter */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Layout principal */
.wrapper {
    display: flex;
    width: 100%;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, var(--primary) 0%, #2d1b3d 100%);
    border-right: 1px solid rgba(255, 107, 53, 0.2);
    padding: 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-xl);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-nextor);
}

/* Sidebar header styles are now inline */

.sidebar a {
    padding: 16px 24px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: var(--transition);
    border-radius: 0 24px 24px 0;
    margin: 4px 0 4px 16px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.sidebar a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-nextor);
    transform: scaleY(0);
    transition: var(--transition);
    border-radius: 0 2px 2px 0;
}

.sidebar a i {
    margin-right: 12px;
    font-size: 18px;
    width: 20px;
    text-align: center;
    transition: var(--transition);
}

.sidebar a:hover {
    background: rgba(255, 107, 53, 0.2);
    color: var(--white);
    transform: translateX(8px);
}

.sidebar a:hover::before {
    transform: scaleY(1);
}

.sidebar a:hover i {
    color: var(--secondary);
    transform: scale(1.1);
}

.sidebar a.active {
    background: rgba(255, 107, 53, 0.3);
    color: var(--white);
    font-weight: 600;
}

.sidebar a.active::before {
    transform: scaleY(1);
}

.sidebar a.active i {
    color: var(--secondary);
}

/* Header */
header {
    background: var(--gradient-nextor);
    padding: 20px 32px;
    box-shadow: var(--shadow-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: none;
    backdrop-filter: blur(20px);
    position: sticky;
    top: 0;
    z-index: 100;
}

header h1 {
    font-size: 24px;
    margin: 0;
    color: var(--white);
    font-weight: 700;
    display: flex;
    align-items: center;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

header h1 i {
    margin-right: 12px;
    color: var(--white);
    font-size: 28px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.header-user {
    display: flex;
    align-items: center;
    gap: 16px;
    color: var(--white);
    font-weight: 500;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header-user i {
    color: var(--white);
    font-size: 20px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* Main */
.main {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: var(--gray-50);
}

.container {
    flex: 1;
    padding: 32px;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Cards */
.card {
    background: var(--white);
    padding: 32px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: 24px;
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--gray-300);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--gray-200);
}

.card-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
}

.card-title i {
    margin-right: 12px;
    color: var(--secondary);
    font-size: 24px;
}

/* Botões */
.btn {
    background: var(--secondary);
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    vertical-align: middle;
    line-height: 1.5;
    min-height: 40px;
    box-sizing: border-box;
}

.btn:hover {
    background: #e55a3d;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.btn i {
    margin-right: 6px;
    font-size: 14px;
    vertical-align: middle;
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 107, 53, 0.2);
}

.btn-primary {
    background: #ff6b35;
}

.btn-primary:hover {
    background: #e55a3d;
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
}

.btn-success:hover {
    background: #218838;
}

.btn-danger {
    background: #dc3545;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-outline {
    background: transparent;
    color: #ff6b35;
    border: 2px solid #ff6b35;
}

.btn-outline:hover {
    background: #ff6b35;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
}

.btn-lg {
    padding: 14px 28px;
    font-size: 16px;
    min-height: 48px;
}

/* Botão flutuante (FAB) */
.fab {
    position: fixed;
    bottom: 32px;
    right: 32px;
    background: var(--gradient-nextor);
    color: var(--white);
    font-size: 24px;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
    z-index: 1000;
    border: none;
    cursor: pointer;
}

.fab::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: var(--gradient-nextor-light);
    opacity: 0;
    transition: var(--transition);
}

.fab:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 25px 50px -12px rgba(255, 107, 53, 0.5);
}

.fab:hover::before {
    opacity: 1;
}

.fab i {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    line-height: 1;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--white);
    padding: 32px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stat-card:nth-child(2)::before {
    background: var(--gradient-secondary);
}

.stat-card:nth-child(3)::before {
    background: var(--gradient-success);
}

.stat-card:nth-child(4)::before {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.stat-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white);
    background: var(--gradient-primary);
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--gradient-secondary);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--gradient-success);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: var(--gray-700);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: var(--gray-600);
    margin: 8px 0 0 0;
    font-weight: 500;
}

.stat-change {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    margin-top: 8px;
}

.stat-change.positive {
    background: #dcfce7;
    color: #166534;
}

.stat-change.negative {
    background: #fef2f2;
    color: #dc2626;
}

.stat-change i {
    margin-right: 4px;
    font-size: 10px;
}

/* Grids de cards */
.produtos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
}

.produto-card {
    background: var(--white);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.produto-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 18px rgba(0,0,0,0.15);
}

.produto-card h3 {
    margin: 0;
    font-size: 18px;
    color: var(--primary);
    display: flex;
    align-items: center;
}

.produto-card h3 i {
    margin-right: 8px;
    color: var(--secondary);
}

.produto-preco {
    font-size: 20px;
    font-weight: bold;
    color: var(--secondary);
    margin: 10px 0;
}

/* Estoque/Badges */
.estoque {
    font-size: 14px;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-block;
    margin-top: 10px;
}

.estoque-alto {
    background: #d0f0fd;
    color: #0275d8;
}

.estoque-medio {
    background: #fff3cd;
    color: #856404;
}

.estoque-baixo {
    background: #f8d7da;
    color: #721c24;
}

.badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

.badge-success { background: #d4edda; color: #155724; }
.badge-warning { background: #fff3cd; color: #856404; }
.badge-danger { background: #f8d7da; color: #721c24; }

/* Ações nos cards */
.card-actions {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
}

.card-actions a {
    flex: 1;
    margin: 0 5px;
    text-align: center;
}

/* Tabelas */
.table-container {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

table th {
    background: var(--gray-50);
    color: var(--gray-900);
    padding: 20px 24px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid var(--gray-200);
}

table td {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-100);
    background: var(--white);
    transition: var(--transition);
    font-size: 14px;
    color: var(--gray-700);
}

table tr:last-child td {
    border-bottom: none;
}

table tr:hover td {
    background: var(--gray-50);
}

table tbody tr {
    transition: var(--transition);
}

table tbody tr:hover {
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

/* Formulários */
.form-group {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--gray-900);
    font-weight: 600;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 16px 20px;
    border-radius: var(--border-radius);
    border: 2px solid var(--gray-200);
    font-size: 16px;
    transition: var(--transition);
    background: var(--white);
    color: var(--gray-900);
    font-family: inherit;
}

.form-control:focus {
    border-color: var(--secondary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: var(--white);
}

.form-control:hover {
    border-color: var(--gray-300);
}

.form-control::placeholder {
    color: var(--gray-400);
}

/* Input com ícone */
.input-group {
    position: relative;
}

.input-group .form-control {
    padding-left: 52px;
}

.input-group i {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: 18px;
    transition: var(--transition);
}

.input-group .form-control:focus + i,
.input-group .form-control:focus ~ i {
    color: var(--secondary);
}

/* Textarea */
textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* Select */
select.form-control {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 48px;
    appearance: none;
}

/* Footer */
footer {
    text-align: center;
    padding: 20px;
    background: var(--white);
    font-size: 14px;
    color: var(--gray-500);
    border-top: 1px solid var(--gray-200);
    margin-top: auto;
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

/* Responsivo */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }

    .container {
        padding: 24px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 70px;
    }

    .sidebar h2,
    .sidebar a span {
        display: none;
    }

    .sidebar a {
        justify-content: center;
        margin: 4px 0 4px 8px;
        border-radius: 12px;
    }

    .main {
        margin-left: 70px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .container {
        padding: 20px;
    }

    header {
        padding: 16px 20px;
    }

    header h1 {
        font-size: 20px;
    }

    .fab {
        bottom: 20px;
        right: 20px;
        width: 56px;
        height: 56px;
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 60px;
    }

    .main {
        margin-left: 60px;
    }

    .container {
        padding: 16px;
    }

    .card {
        padding: 20px;
    }

    .stat-card {
        padding: 24px;
    }

    .stat-value {
        font-size: 28px;
    }

    header h1 {
        font-size: 18px;
    }

    .header-user span {
        display: none;
    }
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--secondary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Formulários Modernos */
.form-container {
    max-width: 800px;
    margin: 0 auto;
}

.form-modern {
    padding: 0;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.form-group {
    position: relative;
}

.form-group-full {
    grid-column: 1 / -1;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 16px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
    background: var(--white);
    color: var(--gray-900);
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-control::placeholder {
    color: var(--gray-400);
}

.form-group.focused .form-label {
    color: var(--secondary);
}

.form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    padding-top: 24px;
    border-top: 1px solid var(--gray-200);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--gray-200);
    min-height: 60px;
}

.page-title h1 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 28px;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 8px;
}

.page-title p {
    color: var(--gray-600);
    font-size: 16px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-shrink: 0;
}

/* Responsividade do header */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .page-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .page-title h1 {
        font-size: 24px;
    }
}

/* =====================================================
   ESTOQUE AVANÇADO
   ===================================================== */

.estoque-resumo {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.estoque-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

@media (max-width: 1024px) {
    .estoque-grid {
        grid-template-columns: 1fr;
    }
}

.empty-state-mini {
    text-align: center;
    padding: 20px;
    color: var(--gray-500);
}

.empty-state-mini i {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--gray-400);
}

.produtos-lista {
    max-height: 300px;
    overflow-y: auto;
}

.produto-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--gray-200);
}

.produto-item:last-child {
    border-bottom: none;
}

.produto-info h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--gray-900);
}

.categoria {
    font-size: 12px;
    font-weight: 500;
}

.produto-estoque {
    text-align: right;
}

.estoque-atual {
    font-size: 16px;
    font-weight: 700;
    color: var(--danger);
}

.estoque-minimo {
    font-size: 12px;
    color: var(--gray-500);
}

.movimentacoes-lista {
    max-height: 300px;
    overflow-y: auto;
}

.movimentacao-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--gray-200);
}

.movimentacao-item:last-child {
    border-bottom: none;
}

.mov-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.mov-icon.entrada {
    background: var(--success);
}

.mov-icon.saida {
    background: var(--danger);
}

.mov-icon.ajuste {
    background: var(--warning);
}

.mov-info h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--gray-900);
}

.mov-detalhes {
    font-size: 12px;
    color: var(--gray-600);
    display: block;
    margin-bottom: 2px;
}

.mov-data {
    font-size: 11px;
    color: var(--gray-500);
}

.categorias-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.categoria-card {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: white;
}

.categoria-header {
    padding: 12px 16px;
    color: white;
    text-align: center;
}

.categoria-header h4 {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

.categoria-stats {
    padding: 16px;
    display: flex;
    justify-content: space-between;
}

.stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: var(--gray-500);
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: var(--gray-900);
}

/* =====================================================
   MOVIMENTAÇÕES DE ESTOQUE
   ===================================================== */

.movimentacao-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 1024px) {
    .movimentacao-grid {
        grid-template-columns: 1fr;
    }
}

.movimentacao-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

@media (max-width: 768px) {
    .movimentacao-form .form-row {
        grid-template-columns: 1fr;
    }
}

.mov-detalhes {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 4px;
}

.mov-tipo {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.mov-quantidade {
    background: var(--primary);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
}

.mov-motivo {
    color: var(--gray-600);
    font-size: 12px;
}

.mov-meta {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 4px;
}

.mov-data {
    font-size: 11px;
    color: var(--gray-500);
}

.mov-doc {
    font-size: 11px;
    color: var(--primary);
    background: rgba(255, 107, 53, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.mov-obs {
    font-size: 12px;
    color: var(--gray-600);
    background: var(--gray-50);
    padding: 8px;
    border-radius: 4px;
    margin-top: 8px;
}

.mov-obs i {
    margin-right: 6px;
    color: var(--gray-400);
}

.mov-estoque {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--gray-600);
    flex-shrink: 0;
}

.estoque-anterior {
    background: var(--gray-100);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
}

.estoque-atual {
    background: var(--success);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
}

.mov-estoque i {
    color: var(--gray-400);
    font-size: 10px;
}

/* Alertas */
.alert {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-danger {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

/* Select customizado */
.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}
