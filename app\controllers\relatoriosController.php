<?php
require_once(__DIR__ . "/../config/conexao.php");

function relatorioClientes($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->query("SELECT * FROM clientes ORDER BY nome");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function relatorioPedidos($empresaBanco, $dataInicio, $dataFim) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("SELECT p.*, c.nome as cliente_nome
                            FROM pedidos p
                            JOIN clientes c ON c.id = p.cliente_id
                            WHERE p.data BETWEEN :inicio AND :fim
                            ORDER BY p.data ASC");
    $stmt->execute([":inicio" => $dataInicio, ":fim" => $dataFim]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function relatorioFinanceiro($empresaBanco, $status = null) {
    $conn = getEmpresaConnection($empresaBanco);
    if ($status) {
        $stmt = $conn->prepare("SELECT * FROM financeiro WHERE status = :status ORDER BY vencimento ASC");
        $stmt->execute([":status" => $status]);
    } else {
        $stmt = $conn->query("SELECT * FROM financeiro ORDER BY vencimento ASC");
    }
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
