<?php
session_start();
require_once(__DIR__ . "/../app/controllers/relatoriosController.php");

$status = $_GET["status"] ?? null;
$lancamentos = relatorioFinanceiro($_SESSION["empresa"], $status);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Relatório Financeiro</title>
</head>
<body>
    <h1>Relatório Financeiro</h1>
    <form method="GET">
        Status:
        <select name="status">
            <option value="">Todos</option>
            <option value="pendente" <?= $status=="pendente"?"selected":"" ?>>Pendente</option>
            <option value="pago" <?= $status=="pago"?"selected":"" ?>>Pago</option>
            <option value="cancelado" <?= $status=="cancelado"?"selected":"" ?>>Cancelado</option>
        </select>
        <button type="submit">Filtrar</button>
    </form>
    <hr>
    <table border="1" cellpadding="5" cellspacing="0">
        <tr><th>ID</th><th>Tipo</th><th>Descrição</th><th>Valor</th><th>Vencimento</th><th>Status</th></tr>
        <?php foreach ($lancamentos as $l): ?>
        <tr>
            <td><?= $l["id"] ?></td>
            <td><?= ucfirst($l["tipo"]) ?></td>
            <td><?= $l["descricao"] ?></td>
            <td>R$ <?= number_format($l["valor"], 2, ',', '.') ?></td>
            <td><?= $l["vencimento"] ?></td>
            <td><?= $l["status"] ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
</body>
</html>
