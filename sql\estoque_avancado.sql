-- =====================================================
-- NEXTOR ERP - SISTEMA DE ESTOQUE AVANÇADO
-- =====================================================

-- Tabela de Categorias de Produtos
CREATE TABLE IF NOT EXISTS categorias (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    descricao TEXT,
    cor VARCHAR(7) DEFAULT '#ff6b35', -- Cor para identificação visual
    icone VARCHAR(50) DEFAULT 'fas fa-tag', -- Ícone FontAwesome
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Adicionar colunas na tabela produtos (PostgreSQL)
DO $$
BEGIN
    -- Adicionar categoria_id
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='produtos' AND column_name='categoria_id') THEN
        ALTER TABLE produtos ADD COLUMN categoria_id INTEGER;
    END IF;

    -- Adicionar codigo_barras
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='produtos' AND column_name='codigo_barras') THEN
        ALTER TABLE produtos ADD COLUMN codigo_barras VARCHAR(50);
    END IF;

    -- Adicionar estoque_minimo
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='produtos' AND column_name='estoque_minimo') THEN
        ALTER TABLE produtos ADD COLUMN estoque_minimo INTEGER DEFAULT 5;
    END IF;

    -- Adicionar estoque_maximo
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='produtos' AND column_name='estoque_maximo') THEN
        ALTER TABLE produtos ADD COLUMN estoque_maximo INTEGER DEFAULT 100;
    END IF;

    -- Adicionar unidade
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='produtos' AND column_name='unidade') THEN
        ALTER TABLE produtos ADD COLUMN unidade VARCHAR(10) DEFAULT 'UN';
    END IF;

    -- Adicionar ativo
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='produtos' AND column_name='ativo') THEN
        ALTER TABLE produtos ADD COLUMN ativo BOOLEAN DEFAULT true;
    END IF;

    -- Adicionar created_at
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='produtos' AND column_name='created_at') THEN
        ALTER TABLE produtos ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
    END IF;

    -- Adicionar updated_at
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='produtos' AND column_name='updated_at') THEN
        ALTER TABLE produtos ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
    END IF;
END $$;

-- Tabela de Movimentações de Estoque
CREATE TABLE IF NOT EXISTS movimentacoes_estoque (
    id SERIAL PRIMARY KEY,
    produto_id INTEGER NOT NULL REFERENCES produtos(id),
    tipo VARCHAR(20) NOT NULL, -- 'entrada', 'saida', 'ajuste', 'transferencia'
    quantidade INTEGER NOT NULL,
    quantidade_anterior INTEGER NOT NULL,
    quantidade_atual INTEGER NOT NULL,
    motivo VARCHAR(100), -- 'venda', 'compra', 'devolucao', 'perda', 'ajuste_inventario'
    observacoes TEXT,
    usuario VARCHAR(100), -- Quem fez a movimentação
    documento VARCHAR(50), -- Número da nota, pedido, etc
    data_movimentacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inserir categorias padrão
INSERT INTO categorias (nome, descricao, cor, icone) VALUES
('Eletrônicos', 'Produtos eletrônicos e tecnologia', '#3b82f6', 'fas fa-laptop'),
('Roupas', 'Vestuário e acessórios', '#ec4899', 'fas fa-tshirt'),
('Casa e Jardim', 'Produtos para casa e decoração', '#10b981', 'fas fa-home'),
('Esportes', 'Artigos esportivos e fitness', '#f59e0b', 'fas fa-dumbbell'),
('Livros', 'Livros e material educativo', '#8b5cf6', 'fas fa-book'),
('Alimentação', 'Produtos alimentícios', '#ef4444', 'fas fa-utensils'),
('Beleza', 'Cosméticos e produtos de beleza', '#f97316', 'fas fa-heart'),
('Automóveis', 'Peças e acessórios automotivos', '#6b7280', 'fas fa-car');

-- Atualizar produtos existentes com categorias
UPDATE produtos SET categoria_id = 1 WHERE descricao ILIKE '%smartphone%' OR descricao ILIKE '%notebook%' OR descricao ILIKE '%tablet%';
UPDATE produtos SET categoria_id = 2 WHERE descricao ILIKE '%camisa%' OR descricao ILIKE '%calça%' OR descricao ILIKE '%vestido%';
UPDATE produtos SET categoria_id = 3 WHERE descricao ILIKE '%mesa%' OR descricao ILIKE '%cadeira%' OR descricao ILIKE '%sofá%';
UPDATE produtos SET categoria_id = 4 WHERE descricao ILIKE '%tênis%' OR descricao ILIKE '%bola%';
UPDATE produtos SET categoria_id = 6 WHERE descricao ILIKE '%café%' OR descricao ILIKE '%açúcar%';

-- Gerar códigos de barras para produtos existentes
UPDATE produtos SET codigo_barras = '789' || LPAD(id::text, 10, '0') WHERE codigo_barras IS NULL;

-- Inserir algumas movimentações de exemplo
INSERT INTO movimentacoes_estoque (produto_id, tipo, quantidade, quantidade_anterior, quantidade_atual, motivo, usuario, documento)
SELECT 
    id,
    'entrada',
    estoque,
    0,
    estoque,
    'estoque_inicial',
    'Sistema',
    'EST-' || LPAD(id::text, 6, '0')
FROM produtos 
WHERE id <= 10;

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_movimentacoes_produto_id ON movimentacoes_estoque(produto_id);
CREATE INDEX IF NOT EXISTS idx_movimentacoes_data ON movimentacoes_estoque(data_movimentacao);
CREATE INDEX IF NOT EXISTS idx_movimentacoes_tipo ON movimentacoes_estoque(tipo);
CREATE INDEX IF NOT EXISTS idx_produtos_categoria ON produtos(categoria_id);
CREATE INDEX IF NOT EXISTS idx_produtos_codigo_barras ON produtos(codigo_barras);
